
import { useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { toast } from "@/hooks/use-toast";

export const useRegenerateCover = () => {
  const [isRegenerating, setIsRegenerating] = useState(false);

  const regenerateCover = async (tutorialId: string) => {
    try {
      setIsRegenerating(true);

      const { data: session } = await supabase.auth.getSession();
      if (!session.session) {
        toast({
          title: "Authentication Required",
          description: "Please sign in to regenerate the cover image",
          variant: "destructive"
        });
        return false;
      }

      const { data, error } = await supabase.functions.invoke(
        'regenerate-tutorial-cover',
        {
          body: { tutorialId }
        }
      );

      if (error) {
        console.error("Error regenerating cover:", error);
        toast({
          title: "Error",
          description: "Failed to regenerate cover image",
          variant: "destructive"
        });
        return false;
      }

      toast({
        title: "Success",
        description: "Cover image regenerated successfully",
      });
      
      return true;
    } catch (error) {
      console.error("Error regenerating cover:", error);
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      });
      return false;
    } finally {
      setIsRegenerating(false);
    }
  };

  return { regenerateCover, isRegenerating };
};
