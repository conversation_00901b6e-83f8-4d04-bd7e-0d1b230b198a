
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.45.0";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

const supabaseUrl = Deno.env.get('SUPABASE_URL')!;
const supabaseServiceKey = Deno.env.get('SUPABASE_SERVICE_ROLE_KEY')!;

const supabase = createClient(supabaseUrl, supabaseServiceKey);

serve(async (req: Request) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    console.log('[SETUP-CRON] Setting up monthly reset cron job');

    // First, unschedule any existing job with the same name
    const { error: unscheduleError } = await supabase.rpc('cron.unschedule', {
      job_name: 'monthly-tutorial-reset'
    });

    if (unscheduleError && !unscheduleError.message.includes('does not exist')) {
      console.error('[SETUP-CRON] Error unscheduling existing job:', unscheduleError);
    }

    // Schedule the new cron job to run daily at 2 AM UTC
    const { error: scheduleError } = await supabase.rpc('cron.schedule', {
      job_name: 'monthly-tutorial-reset',
      schedule: '0 2 * * *', // Daily at 2 AM UTC
      command: `
        SELECT net.http_post(
          url := '${supabaseUrl}/functions/v1/reset-monthly-counts',
          headers := '{"Content-Type": "application/json", "Authorization": "Bearer ${Deno.env.get('SUPABASE_ANON_KEY')}"}',
          body := '{"triggered_by": "cron"}'
        );
      `
    });

    if (scheduleError) {
      console.error('[SETUP-CRON] Error scheduling cron job:', scheduleError);
      throw scheduleError;
    }

    console.log('[SETUP-CRON] Cron job scheduled successfully');

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Monthly reset cron job scheduled successfully',
        schedule: '0 2 * * * (Daily at 2 AM UTC)',
        timestamp: new Date().toISOString()
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );

  } catch (error) {
    console.error('[SETUP-CRON] Error:', error);
    
    return new Response(
      JSON.stringify({ 
        error: error instanceof Error ? error.message : 'Unknown error occurred',
        timestamp: new Date().toISOString()
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders },
      }
    );
  }
});
