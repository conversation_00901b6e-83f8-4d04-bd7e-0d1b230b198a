
import React from "react";
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";

export type GenerationStep = {
  id: string;
  title: string;
  description: string;
  status: "pending" | "processing" | "completed" | "error";
};

type GenerationProgressProps = {
  steps: GenerationStep[];
  currentStep: string;
  progress: number;
  onCancel: () => void;
};

const GenerationProgress = ({ steps, currentStep, progress, onCancel }: GenerationProgressProps) => {
  return (
    <Card className="w-full max-w-3xl animate-fade-in">
      <CardHeader>
        <CardTitle>Generating Tutorial</CardTitle>
        <CardDescription>
          Please wait while we process the repository and generate your tutorial
        </CardDescription>
        <Progress value={progress} className="mt-2" />
        <p className="text-sm text-muted-foreground pt-2">
          {progress}% complete
        </p>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-start gap-4">
              <div className={`${
                step.status === "completed" 
                  ? "bg-green-500" 
                  : step.status === "processing" 
                  ? "tutorial-gradient animate-pulse-light" 
                  : "bg-gray-200"
              } step-number`}>
                {index + 1}
              </div>
              <div className="flex-1 space-y-1">
                <p className="font-medium">{step.title}</p>
                <p className="text-sm text-muted-foreground">{step.description}</p>
              </div>
              <div className="text-sm">
                {step.status === "completed" ? (
                  <span className="text-green-500">Completed</span>
                ) : step.status === "processing" ? (
                  <span className="text-tutorial-primary">Processing...</span>
                ) : step.status === "error" ? (
                  <span className="text-red-500">Error</span>
                ) : (
                  <span className="text-gray-400">Pending</span>
                )}
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
};

export default GenerationProgress;
