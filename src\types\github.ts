
export interface GitHubRepoInfo {
  url?: string;
  owner?: string;
  name?: string;
  branch?: string;
  commitSha?: string;
  path?: string;
  programmingLanguage?: string;
}

export interface ParsedGitHubUrl {
  owner: string;
  repo: string;
}

export interface GitHubRepoConfig {
  owner: string;
  repository: string;
  branch: string;
  path: string;
  commit: string;
  programmingLanguage?: string;
}

export interface GitHubRepoInfoTutorialCard {
  name: string;
  full_name: string;
  description: string;
  html_url: string;
  stargazers_count: number;
  forks_count: number;
  watchers_count: number;
  language: string;
  created_at: string;
  updated_at: string;
  private?: boolean;
  owner: {
    login: string;
    avatar_url: string;
    html_url: string;
  };
  topics: string[];
  license?: {
    name: string;
  };
}