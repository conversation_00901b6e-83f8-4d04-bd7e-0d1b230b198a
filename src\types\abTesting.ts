// A/B Testing Types
export interface ABTestVariant {
  id: string;
  name: string;
  weight: number; // Percentage of traffic (0-100)
  config: Record<string, any>; // Variant-specific configuration
}

export interface ABTestExperiment {
  id: string;
  name: string;
  description?: string;
  status: 'draft' | 'active' | 'paused' | 'completed';
  startDate?: Date;
  endDate?: Date;
  variants: ABTestVariant[];
  targetAudience?: {
    userTypes?: ('new' | 'returning' | 'trial' | 'subscribed')[];
    percentage?: number; // Percentage of users to include in experiment
  };
  conversionGoals?: string[]; // Event names to track as conversions
  metadata?: Record<string, any>;
}

export interface ABTestAssignment {
  userId?: string;
  sessionId: string;
  experimentId: string;
  variantId: string;
  assignedAt: Date;
  isConverted?: boolean;
  conversionEvents?: string[];
}

export interface ABTestEvent {
  experimentId: string;
  variantId: string;
  userId?: string;
  sessionId: string;
  eventType: 'view' | 'click' | 'conversion' | 'custom';
  eventName?: string;
  eventData?: Record<string, any>;
  timestamp: Date;
}

export interface ABTestResults {
  experimentId: string;
  variants: {
    variantId: string;
    name: string;
    views: number;
    conversions: number;
    conversionRate: number;
    confidence?: number;
  }[];
  totalViews: number;
  totalConversions: number;
  isStatisticallySignificant?: boolean;
  winningVariant?: string;
}

// Hook return types
export interface UseABTestReturn<T = any> {
  variant: ABTestVariant | null;
  config: T;
  isLoading: boolean;
  trackEvent: (eventType: string, eventData?: Record<string, any>) => void;
  trackConversion: (conversionGoal?: string) => void;
}

// Configuration types for specific experiments
export interface HeroHeadlineConfig {
  title: string;
  subtitle: string;
  ctaText: string;
  emphasizedWords?: string[];
}
