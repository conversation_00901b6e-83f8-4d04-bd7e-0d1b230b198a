// src/Agents/Code2Documentation/flow/flow.ts
import { Flow } from "../../../pocketflow";
import {
  AnalyzeRelationships,
  IdentifyAbstractions,
  FetchRepo,
  OrderChapters,
  WriteChapters,
  CombineTutorial,
} from "../nodes";

import { SharedStore } from "../types";

/**
 * Creates and returns the codebase tutorial generation flow.
 * @returns Flow instance
 */
export function create_tutorial_flow(): Flow {
  
  const maxRetries = 5;
  const waitTime = 20;
  
  // Instantiate nodes
  const fetch_repo = new FetchRepo();

  const identify_abstractions = new IdentifyAbstractions(maxRetries,waitTime);
  const analyze_relationships = new AnalyzeRelationships(maxRetries,waitTime);
  const order_chapters = new OrderChapters(maxRetries,waitTime);

  const write_chapters = new WriteChapters(maxRetries,waitTime); // This is a BatchNode
  const combine_tutorial = new CombineTutorial();

  // Connect nodes in sequence
  fetch_repo.next(identify_abstractions);
  identify_abstractions.next(analyze_relationships);
  analyze_relationships.next(order_chapters);
  order_chapters.next(write_chapters);
  write_chapters.next(combine_tutorial);

  //Create flow starting with input node
  const tutorial_flow = new Flow<SharedStore>(fetch_repo);
  return tutorial_flow;
}
