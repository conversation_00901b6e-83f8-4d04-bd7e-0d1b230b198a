import { Flow } from '../index';
import { DiagramFlow, DiagramOptions } from './FlowDiagram';

/**
 * Generate a diagram from a Flow instance
 * 
 * @param flow The flow to visualize
 * @param filename Path where the diagram should be saved
 * @param options Customization options for the diagram
 */
export async function generateDiagram(
  flow: Flow,
  filename: string,
  options?: DiagramOptions
): Promise<void> {
  // Create a DiagramFlow with the same start node
  const diagramFlow = new DiagramFlow(flow.start);
  
  // Export the diagram
  await diagramFlow.export_png(filename, options);
}

/**
 * Convert a regular Flow to a DiagramFlow
 * 
 * @param flow The flow to convert
 * @returns A DiagramFlow instance with the same structure
 */
export function asDiagramFlow<S = unknown, P extends Record<string, any> = Record<string, any>>(
  flow: Flow<S, P>
): DiagramFlow<S, P> {
  return new DiagramFlow<S, P>(flow.start);
}