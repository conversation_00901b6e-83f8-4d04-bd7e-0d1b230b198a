import type { ABTestExperiment } from '@/types/abTesting';

// Hero Headline A/B Test Configuration
export const HERO_HEADLINE_EXPERIMENT: ABTestExperiment = {
  id: 'hero-headline-v1',
  name: 'Hero Section Headline Test',
  description: 'Testing different headline variations to improve conversion rates',
  status: 'active',
  startDate: new Date('2024-01-15'),
  endDate: new Date('2024-02-15'),
  variants: [
    {
      id: 'control',
      name: 'Original Headline',
      weight: 50,
      config: {
        title: 'CodeTutorPro',
        subtitle: 'Transform any GitHub repository into AI-powered step-by-step tutorials for developers. Stop struggling with complex codebases and start learning efficiently.',
        ctaText: 'Get Early Access',
        emphasizedWords: ['GitHub repository', 'AI-powered step-by-step tutorials']
      }
    },
    {
      id: 'benefit-focused',
      name: 'Benefit-Focused Headline',
      weight: 50,
      config: {
        title: 'Learn Any Codebase in Minutes, Not Hours',
        subtitle: 'CodeTutorPro uses advanced AI to transform complex GitHub repositories into beginner-friendly, step-by-step learning experiences. Master new technologies faster than ever.',
        ctaText: 'Start Learning Now',
        emphasizedWords: ['Minutes, Not Hours', 'advanced AI', 'beginner-friendly']
      }
    }
  ],
  targetAudience: {
    userTypes: ['new', 'returning'],
    percentage: 100 // Include all users
  },
  conversionGoals: [
    'waitlist_signup',
    'modal_open',
    'cta_click'
  ],
  metadata: {
    hypothesis: 'A benefit-focused headline emphasizing time savings will increase conversion rates',
    successMetric: 'waitlist_signup',
    minimumSampleSize: 1000,
    expectedLift: 15
  }
};

// Additional experiment configurations can be added here
export const FEATURE_HIGHLIGHTS_EXPERIMENT: ABTestExperiment = {
  id: 'feature-highlights-v1',
  name: 'Feature Highlights Section Test',
  description: 'Testing different ways to present key features',
  status: 'draft',
  variants: [
    {
      id: 'control',
      name: 'Current Feature Layout',
      weight: 50,
      config: {
        layout: 'grid',
        showIcons: true,
        showAnimations: true
      }
    },
    {
      id: 'simplified',
      name: 'Simplified Feature List',
      weight: 50,
      config: {
        layout: 'list',
        showIcons: false,
        showAnimations: false
      }
    }
  ],
  targetAudience: {
    percentage: 50 // Only test with 50% of users
  },
  conversionGoals: ['feature_engagement', 'scroll_depth'],
  metadata: {
    hypothesis: 'Simplified feature presentation will reduce cognitive load and improve engagement'
  }
};

// CTA Button Test
export const CTA_BUTTON_EXPERIMENT: ABTestExperiment = {
  id: 'cta-button-v1',
  name: 'CTA Button Variation Test',
  description: 'Testing different CTA button texts and styles',
  status: 'draft',
  variants: [
    {
      id: 'control',
      name: 'Get Early Access',
      weight: 33.33,
      config: {
        text: 'Get Early Access',
        style: 'primary',
        icon: 'zap'
      }
    },
    {
      id: 'urgency',
      name: 'Join Now - Limited Spots',
      weight: 33.33,
      config: {
        text: 'Join Now - Limited Spots',
        style: 'primary',
        icon: 'clock'
      }
    },
    {
      id: 'value',
      name: 'Start Learning Free',
      weight: 33.34,
      config: {
        text: 'Start Learning Free',
        style: 'primary',
        icon: 'play'
      }
    }
  ],
  conversionGoals: ['cta_click', 'waitlist_signup'],
  metadata: {
    hypothesis: 'Adding urgency or value proposition to CTA will increase click-through rates'
  }
};

// Export all experiments for easy management
export const ALL_EXPERIMENTS = [
  HERO_HEADLINE_EXPERIMENT,
  FEATURE_HIGHLIGHTS_EXPERIMENT,
  CTA_BUTTON_EXPERIMENT
];

// Helper function to get experiment by ID
export function getExperimentConfig(experimentId: string): ABTestExperiment | undefined {
  return ALL_EXPERIMENTS.find(exp => exp.id === experimentId);
}

// Helper function to get active experiments
export function getActiveExperiments(): ABTestExperiment[] {
  return ALL_EXPERIMENTS.filter(exp => exp.status === 'active');
}
