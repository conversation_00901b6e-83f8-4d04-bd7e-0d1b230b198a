import React, { createContext, useContext, useEffect, ReactNode } from 'react';
import { useAuth } from '@/hooks/useAuth';
import { abTestingService } from '@/services/abTesting';
import { useABTestStore } from '@/stores/useABTestStore';

interface ABTestContextType {
  isInitialized: boolean;
}

const ABTestContext = createContext<ABTestContextType>({
  isInitialized: false
});

interface ABTestProviderProps {
  children: ReactNode;
}

export const ABTestProvider: React.FC<ABTestProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [isInitialized, setIsInitialized] = React.useState(false);

  useEffect(() => {
    // Set user ID in the A/B testing service when user changes
    if (user?.id) {
      abTestingService.setUserId(user.id);
    }
    
    // Mark as initialized
    setIsInitialized(true);
  }, [user?.id]);

  const contextValue: ABTestContextType = {
    isInitialized
  };

  return (
    <ABTestContext.Provider value={contextValue}>
      {children}
    </ABTestContext.Provider>
  );
};

export const useABTestContext = () => {
  const context = useContext(ABTestContext);
  if (!context) {
    throw new Error('useABTestContext must be used within an ABTestProvider');
  }
  return context;
};

// HOC for components that need A/B testing
export function withABTest<P extends object>(
  Component: React.ComponentType<P>
): React.ComponentType<P> {
  return function ABTestWrappedComponent(props: P) {
    const { isInitialized } = useABTestContext();
    
    if (!isInitialized) {
      return null; // or a loading spinner
    }
    
    return <Component {...props} />;
  };
}
