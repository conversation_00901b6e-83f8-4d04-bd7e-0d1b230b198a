
import React, { useState } from "react";
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { useUserSettings } from "@/hooks/useUserSettings";
import { useSubscription } from "@/hooks/useSubscription";
import { Skeleton } from "@/components/ui/skeleton";
import { Clock, Zap, Crown, Settings } from "lucide-react";
import { useNavigate } from "react-router-dom";

const SettingsForm = () => {
  const { settings, setSettings, saveSettings, userDetails, loading, saving } = useUserSettings();
  const { subscribed, subscription_tier, subscription_end, loading: subscriptionLoading } = useSubscription();
  const [localSettings, setLocalSettings] = useState(settings);
  const navigate = useNavigate();

  React.useEffect(() => {
    setLocalSettings(settings);
  }, [settings]);

  const handleSave = () => {
    saveSettings(localSettings);
  };

  const handleRestore = () => {
    setLocalSettings({
      github: '',
      max_file_size: 100,
      default_llm: ''
    });
  };

  const handleManageSubscription = () => {
    navigate('/dashboard/subscription');
  };

  const isInTrial = userDetails.trial_end_date && new Date(userDetails.trial_end_date) > new Date();
  const daysLeft = userDetails.trial_end_date 
    ? Math.max(0, Math.ceil((new Date(userDetails.trial_end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24)))
    : 0;

  if (loading) {
    return (
      <Card className="w-full max-w-3xl">
        <CardHeader>
          <CardTitle>Settings</CardTitle>
          <CardDescription>Configure parameters for tutorial generation</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
            <Skeleton className="h-20 w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full max-w-3xl">
      <CardHeader>
        <CardTitle>Settings</CardTitle>
        <CardDescription>
          Configure parameters for tutorial generation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          {/* Subscription Section */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium flex items-center gap-2">
              <Crown className="h-5 w-5 text-primary" />
              Subscription
            </h3>
            {subscriptionLoading ? (
              <Skeleton className="h-20 w-full" />
            ) : (
              <div className="p-4 border rounded-lg bg-gray-50">
                <div className="flex items-center justify-between mb-3">
                  <div className="flex items-center space-x-2">
                    {subscribed ? (
                      <>
                        <Crown className="h-4 w-4 text-primary" />
                        <span className="font-medium text-primary">
                          {subscription_tier || 'Premium'} Plan
                        </span>
                        <Badge variant="default" className="bg-green-600">
                          Active
                        </Badge>
                      </>
                    ) : isInTrial ? (
                      <>
                        <Zap className="h-4 w-4 text-blue-600" />
                        <span className="font-medium text-blue-900">Trial Account</span>
                        <Badge variant="outline" className="text-blue-700 border-blue-300">
                          <Clock className="h-3 w-3 mr-1" />
                          {daysLeft} days left
                        </Badge>
                      </>
                    ) : (
                      <>
                        <Settings className="h-4 w-4 text-gray-600" />
                        <span className="font-medium text-gray-600">Free Account</span>
                        <Badge variant="outline">
                          No Active Subscription
                        </Badge>
                      </>
                    )}
                  </div>
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={handleManageSubscription}
                  >
                    {subscribed ? 'Manage' : 'Upgrade'}
                  </Button>
                </div>
                <div className="text-sm text-gray-600">
                  {subscribed && subscription_end && (
                    <p>Renews on {new Date(subscription_end).toLocaleDateString()}</p>
                  )}
                  {isInTrial && (
                    <>
                      <p>Tutorials created: {userDetails.tutorials_created_count} of {userDetails.trial_tutorials_limit}</p>
                      {userDetails.tutorials_created_count >= userDetails.trial_tutorials_limit && (
                        <p className="text-orange-600 font-medium mt-1">
                          Trial limit reached - Upgrade to create more tutorials
                        </p>
                      )}
                    </>
                  )}
                  {!subscribed && !isInTrial && (
                    <p>Subscribe to unlock premium features and unlimited access.</p>
                  )}
                </div>
              </div>
            )}
          </div>

          {/* Trial Status Section - only show if in trial */}
          {isInTrial && (
            <div className="space-y-4">
              <h3 className="text-lg font-medium">Account Status</h3>
              <div className="p-4 border rounded-lg bg-blue-50 border-blue-200">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <Zap className="h-4 w-4 text-blue-600" />
                    <span className="font-medium text-blue-900">Trial Account</span>
                  </div>
                  <Badge variant="outline" className="text-blue-700 border-blue-300">
                    <Clock className="h-3 w-3 mr-1" />
                    {daysLeft} days left
                  </Badge>
                </div>
                <div className="text-sm text-blue-800">
                  <p>Tutorials created: {userDetails.tutorials_created_count} of {userDetails.trial_tutorials_limit}</p>
                  {userDetails.tutorials_created_count >= userDetails.trial_tutorials_limit && (
                    <p className="text-orange-600 font-medium mt-1">
                      Trial limit reached - Upgrade to create more tutorials
                    </p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* Profile Settings */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Profile Settings</h3>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="github">GitHub Username</Label>
                <Input
                  id="github"
                  type="text"
                  placeholder="Enter your GitHub username"
                  value={localSettings.github}
                  onChange={(e) => setLocalSettings({ ...localSettings, github: e.target.value })}
                />
                <p className="text-xs text-muted-foreground">
                  Your GitHub username for easier repository access
                </p>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="defaultLlm">Default LLM Model</Label>
                <Select
                  value={localSettings.default_llm}
                  onValueChange={(value) => setLocalSettings({ ...localSettings, default_llm: value })}
                >
                  <SelectTrigger id="defaultLlm">
                    <SelectValue placeholder="Select default model" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="claude-3-sonnet">Claude 3.5 Sonnet</SelectItem>
                    <SelectItem value="gpt-4o">GPT-4o</SelectItem>
                    <SelectItem value="gpt-4o-mini">GPT-4o Mini</SelectItem>
                  </SelectContent>
                </Select>
                <p className="text-xs text-muted-foreground">
                  Default AI model for tutorial generation
                </p>
              </div>
            </div>
          </div>

          {/* File Processing */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">File Processing</h3>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="maxFileSize">Maximum File Size (KB)</Label>
                <Input
                  id="maxFileSize"
                  type="number"
                  min="1"
                  max="10000"
                  value={localSettings.max_file_size}
                  onChange={(e) => setLocalSettings({ ...localSettings, max_file_size: parseInt(e.target.value) || 100 })}
                />
                <p className="text-xs text-muted-foreground">
                  Maximum size for individual files during processing
                </p>
              </div>
            </div>
          </div>

          {/* GitHub Integration */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">GitHub Integration</h3>
            <div className="grid gap-4">
              <div className="grid gap-2">
                <Label htmlFor="githubToken">GitHub Personal Access Token</Label>
                <Input
                  id="githubToken"
                  type="password"
                  placeholder="Enter GitHub token for private repositories"
                />
                <p className="text-xs text-muted-foreground">
                  Required for accessing private GitHub repositories
                </p>
              </div>
            </div>
          </div>

          {/* Display Preferences */}
          <div className="space-y-4">
            <h3 className="text-lg font-medium">Display Preferences</h3>
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="generateDiagrams">Generate Diagrams</Label>
                  <p className="text-xs text-muted-foreground">
                    Include architecture diagrams in tutorials
                  </p>
                </div>
                <Switch id="generateDiagrams" defaultChecked />
              </div>
              <div className="flex items-center justify-between">
                <div>
                  <Label htmlFor="cacheResults">Cache Results</Label>
                  <p className="text-xs text-muted-foreground">
                    Save generated tutorials to improve performance
                  </p>
                </div>
                <Switch id="cacheResults" defaultChecked />
              </div>
            </div>
          </div>
        </div>
      </CardContent>
      <CardFooter className="flex justify-end gap-3">
        <Button variant="outline" onClick={handleRestore}>
          Restore Defaults
        </Button>
        <Button 
          className="tutorial-gradient" 
          onClick={handleSave}
          disabled={saving}
        >
          {saving ? "Saving..." : "Save Settings"}
        </Button>
      </CardFooter>
    </Card>
  );
};

export default SettingsForm;
