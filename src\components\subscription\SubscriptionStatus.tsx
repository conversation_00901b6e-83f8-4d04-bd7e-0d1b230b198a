
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useSubscription } from '@/hooks/useSubscription';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/hooks/useAuth';
import { useToast } from '@/hooks/use-toast';

export const SubscriptionStatus = () => {
  const { 
    subscribed, 
    subscription_tier, 
    subscription_end, 
    trial_end, 
    in_trial, 
    loading, 
    refetchSubscription 
  } = useSubscription();
  const { session } = useAuth();
  const { toast } = useToast();

  const handleManageSubscription = async () => {
    if (!session?.access_token) return;

    try {
      const { data, error } = await supabase.functions.invoke('customer-portal', {
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error) throw error;
      
      // Open customer portal in a new tab
      window.open(data.url, '_blank');
    } catch (error) {
      console.error('Error opening customer portal:', error);
      toast({
        title: 'Error',
        description: 'Failed to open subscription management',
        variant: 'destructive'
      });
    }
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded w-1/4 mb-2"></div>
            <div className="h-3 bg-gray-200 rounded w-1/2"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          Subscription Status
          <Button
            variant="outline"
            size="sm"
            onClick={refetchSubscription}
          >
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent>
        {subscribed ? (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              {in_trial ? (
                <Badge variant="default" className="bg-orange-600">
                  Trial Active
                </Badge>
              ) : (
                <Badge variant="default" className="bg-green-600">
                  Active
                </Badge>
              )}
              <span className="font-medium">{subscription_tier} Plan</span>
            </div>
            
            {in_trial && trial_end && (
              <p className="text-sm text-orange-600">
                Trial ends on {new Date(trial_end).toLocaleDateString()}
              </p>
            )}
            
            {!in_trial && subscription_end && (
              <p className="text-sm text-gray-600">
                Renews on {new Date(subscription_end).toLocaleDateString()}
              </p>
            )}
            
            <Button onClick={handleManageSubscription}>
              Manage Subscription
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <Badge variant="outline">
                No Active Subscription
              </Badge>
            </div>
            <CardDescription>
              Subscribe to unlock premium features and unlimited access.
            </CardDescription>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
