# A/B Testing Infrastructure

This document describes the comprehensive A/B testing infrastructure implemented in CodeTutorPro. The system is designed to be generic, reusable, and easy to implement across different parts of the application.

## Architecture Overview

The A/B testing system consists of several key components:

1. **Database Schema** - Supabase tables for experiments, assignments, and events
2. **Service Layer** - Core business logic for experiment management
3. **State Management** - Zustand store for caching and persistence
4. **React Hooks** - Easy-to-use hooks for component integration
5. **Admin Interface** - Management dashboard for experiments

## Components

### 1. Database Schema (`supabase/migrations/001_ab_testing_schema.sql`)

Three main tables:
- `ab_experiments` - Experiment configurations
- `ab_assignments` - User-to-variant assignments
- `ab_events` - Analytics and tracking data

### 2. Service Layer (`src/services/abTesting.ts`)

Core service providing:
- Experiment retrieval and caching
- Weighted variant assignment
- Event and conversion tracking
- User targeting and bucketing

### 3. State Management (`src/stores/useABTestStore.ts`)

Zustand store handling:
- Experiment and assignment caching
- Persistent storage across sessions
- Loading state management

### 4. React Hooks (`src/hooks/useABTest.ts`)

Three hooks for different use cases:
- `useABTest<T>()` - Full-featured A/B testing
- `useSimpleABTest<T>()` - Predefined variant configurations
- `useFeatureFlag()` - Boolean feature flags

### 5. Provider (`src/components/ABTestProvider.tsx`)

Context provider for:
- User ID synchronization
- Initialization management
- HOC for A/B test components

## Usage Examples

### Basic A/B Test Implementation

```tsx
import { useABTest } from '@/hooks/useABTest';
import type { HeroHeadlineConfig } from '@/types/abTesting';

const MyComponent = () => {
  const defaultConfig: HeroHeadlineConfig = {
    title: 'Default Title',
    subtitle: 'Default subtitle text',
    ctaText: 'Click Here',
    emphasizedWords: ['important', 'words']
  };

  const { config, trackEvent, trackConversion } = useABTest<HeroHeadlineConfig>(
    'my-experiment-id',
    defaultConfig
  );

  const handleClick = () => {
    trackEvent('button_click');
    // Your click logic
  };

  const handleConversion = () => {
    trackConversion('signup');
  };

  return (
    <div>
      <h1>{config.title}</h1>
      <p>{config.subtitle}</p>
      <button onClick={handleClick}>{config.ctaText}</button>
    </div>
  );
};
```

### Simple A/B Test with Predefined Variants

```tsx
import { useSimpleABTest } from '@/hooks/useABTest';

const ButtonTest = () => {
  const variants = {
    control: { color: 'blue', text: 'Sign Up' },
    variant1: { color: 'green', text: 'Get Started' },
    variant2: { color: 'red', text: 'Join Now' }
  };

  const { config, trackConversion } = useSimpleABTest(
    'button-test-v1',
    variants,
    'control'
  );

  return (
    <button 
      style={{ backgroundColor: config.color }}
      onClick={() => trackConversion()}
    >
      {config.text}
    </button>
  );
};
```

### Feature Flag

```tsx
import { useFeatureFlag } from '@/hooks/useABTest';

const FeatureComponent = () => {
  const { config } = useFeatureFlag('new-feature-flag', false);

  if (!config.enabled) {
    return <OldComponent />;
  }

  return <NewComponent />;
};
```

## Experiment Configuration

Experiments are configured in `src/config/abTestExperiments.ts`:

```typescript
export const MY_EXPERIMENT: ABTestExperiment = {
  id: 'my-experiment-v1',
  name: 'My Test Experiment',
  description: 'Testing different approaches',
  status: 'active',
  startDate: new Date('2024-01-15'),
  endDate: new Date('2024-02-15'),
  variants: [
    {
      id: 'control',
      name: 'Control Group',
      weight: 50,
      config: { /* variant config */ }
    },
    {
      id: 'treatment',
      name: 'Treatment Group',
      weight: 50,
      config: { /* variant config */ }
    }
  ],
  targetAudience: {
    userTypes: ['new', 'returning'],
    percentage: 100
  },
  conversionGoals: ['signup', 'purchase'],
  metadata: {
    hypothesis: 'Treatment will increase conversions',
    expectedLift: 15
  }
};
```

## Current Implementation: Hero Headline Test

The first A/B test implemented tests different headline variations in the hero section:

### Variants:
1. **Control**: Original "CodeTutorPro" headline
2. **Benefit-Focused**: "Learn Any Codebase in Minutes, Not Hours"

### Tracking:
- View events (automatic)
- CTA clicks
- Modal opens
- Waitlist signups (conversions)

### Configuration:
- 50/50 traffic split
- All users included
- Active experiment

## Database Setup

1. Run the migration:
```sql
-- Apply the migration file
\i supabase/migrations/001_ab_testing_schema.sql
```

2. Seed experiments:
```bash
# Run the seeding script
npx ts-node scripts/seedABTestExperiments.ts
```

## Admin Interface

Access the A/B test management interface through the admin dashboard:

- View all experiments and their status
- Start, pause, or complete experiments
- View real-time results and conversion rates
- Monitor variant performance

## Best Practices

1. **Statistical Significance**: Run tests until you have sufficient sample size
2. **Single Variable Testing**: Test one element at a time
3. **Clear Hypotheses**: Define what you expect to improve and by how much
4. **Conversion Goals**: Set clear, measurable conversion events
5. **Documentation**: Document all experiments and their results

## Analytics and Reporting

The system tracks:
- **Views**: Automatic when variant is assigned
- **Clicks**: Custom events for interactions
- **Conversions**: Goal completions
- **Conversion Rates**: Calculated automatically
- **Statistical Significance**: Can be calculated with sufficient data

## Future Enhancements

- Multi-variate testing support
- Advanced targeting (geographic, device, etc.)
- Automatic winner selection
- Integration with external analytics
- A/B test scheduling
- Segment-based analysis

## Troubleshooting

### Common Issues:

1. **Experiment not loading**: Check if experiment is active and properly configured
2. **No variant assigned**: Verify user meets targeting criteria
3. **Events not tracking**: Ensure proper event names and user authentication
4. **Results not updating**: Check database permissions and RLS policies

### Debug Mode:

Enable debug logging by setting localStorage:
```javascript
localStorage.setItem('ab_test_debug', 'true');
```

This will log all A/B test operations to the console for debugging purposes.
