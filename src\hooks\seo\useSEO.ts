import { useEffect } from 'react';

export interface SEOData {
  title?: string;
  description?: string;
  keywords?: string;
  canonical?: string;
  ogTitle?: string;
  ogDescription?: string;
  ogImage?: string;
  ogUrl?: string;
  twitterTitle?: string;
  twitterDescription?: string;
  twitterImage?: string;
  structuredData?: object;
  noindex?: boolean;
}

export const useSEO = (seoData: SEOData) => {
  useEffect(() => {
    // Update document title
    if (seoData.title) {
      document.title = seoData.title;
    }

    // Update or create meta tags
    const updateMetaTag = (name: string, content: string, property = false) => {
      const attribute = property ? 'property' : 'name';
      let meta = document.querySelector(`meta[${attribute}="${name}"]`) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        meta.setAttribute(attribute, name);
        document.head.appendChild(meta);
      }
      
      meta.content = content;
    };

    // Update canonical link
    const updateCanonical = (url: string) => {
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      
      if (!canonical) {
        canonical = document.createElement('link');
        canonical.rel = 'canonical';
        document.head.appendChild(canonical);
      }
      
      canonical.href = url;
    };

    // Update structured data
    const updateStructuredData = (data: object) => {
      // Remove existing structured data
      const existingScript = document.querySelector('script[type="application/ld+json"]');
      if (existingScript) {
        existingScript.remove();
      }

      // Add new structured data
      const script = document.createElement('script');
      script.type = 'application/ld+json';
      script.textContent = JSON.stringify(data);
      document.head.appendChild(script);
    };

    // Apply SEO data
    if (seoData.description) {
      updateMetaTag('description', seoData.description);
    }

    if (seoData.keywords) {
      updateMetaTag('keywords', seoData.keywords);
    }

    if (seoData.canonical) {
      updateCanonical(seoData.canonical);
    }

    // Open Graph tags
    if (seoData.ogTitle) {
      updateMetaTag('og:title', seoData.ogTitle, true);
    }

    if (seoData.ogDescription) {
      updateMetaTag('og:description', seoData.ogDescription, true);
    }

    if (seoData.ogImage) {
      updateMetaTag('og:image', seoData.ogImage, true);
    }

    if (seoData.ogUrl) {
      updateMetaTag('og:url', seoData.ogUrl, true);
    }

    // Twitter tags
    if (seoData.twitterTitle) {
      updateMetaTag('twitter:title', seoData.twitterTitle, true);
    }

    if (seoData.twitterDescription) {
      updateMetaTag('twitter:description', seoData.twitterDescription, true);
    }

    if (seoData.twitterImage) {
      updateMetaTag('twitter:image', seoData.twitterImage, true);
    }

    // Robots meta tag
    if (seoData.noindex) {
      updateMetaTag('robots', 'noindex, nofollow');
    } else {
      updateMetaTag('robots', 'index, follow');
    }

    // Structured data
    if (seoData.structuredData) {
      updateStructuredData(seoData.structuredData);
    }

  }, [seoData]);
};

// Default SEO configuration for the application
export const defaultSEO: SEOData = {
  title: 'CodeTutorPro - Transform GitHub Repos into AI-Powered Tutorials | Free Early Access',
  description: 'Stop struggling with complex codebases. CodeTutorPro uses advanced AI to transform any GitHub repository into step-by-step, beginner-friendly tutorials. Join 2,847+ developers on our waiting list for free early access.',
  keywords: 'AI code tutorials, GitHub repository analysis, code learning platform, programming tutorials, AI-powered coding education, beginner programming, code explanation tool, software development learning',
  canonical: 'https://codetutorpro.com/',
  ogTitle: 'CodeTutorPro - Transform GitHub Repos into AI-Powered Tutorials',
  ogDescription: 'Stop struggling with complex codebases. CodeTutorPro uses advanced AI to transform any GitHub repository into step-by-step, beginner-friendly tutorials. Join 2,847+ developers for free early access.',
  ogImage: 'https://codetutorpro.com/og-image.jpg',
  ogUrl: 'https://codetutorpro.com/',
  twitterTitle: 'CodeTutorPro - Transform GitHub Repos into AI-Powered Tutorials',
  twitterDescription: 'Stop struggling with complex codebases. CodeTutorPro uses advanced AI to transform any GitHub repository into step-by-step, beginner-friendly tutorials.',
  twitterImage: 'https://codetutorpro.com/og-image.jpg',
};
