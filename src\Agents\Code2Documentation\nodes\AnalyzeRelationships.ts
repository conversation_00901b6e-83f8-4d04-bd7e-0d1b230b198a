import yaml from 'js-yaml';
import { Node } from '../../../pocketflow';
import { SharedStore } from '../types';

import { getContentForIndices } from '../utils/fileHelpers';
import { emitGraphStatus, emitProgress } from "../utils/events";
import { callLlm_openrouter } from '@/Agents/shared/callLlm_openrouter';
import { buildPrompt } from "../../../pocketflow/utils/buildPrompt";
import { ANALYZE_RELATIONSHIPS_PROMPT } from "../prompts/analyzeRelationships";

export class AnalyzeRelationships extends Node<SharedStore> {
  private curRetry = 0;

  async prep(shared: SharedStore) {
    // Emit graph status to indicate this node is starting
    emitGraphStatus("AnalyzeRelationships", 0, "Starting relationship analysis");

    const abstractions = shared.abstractions || [];
    const files_data = shared.files || [];
    const project_name = shared.project_name!;
    const language = shared.language || 'english';
    const use_cache = shared.use_cache ?? true;

    // Get the actual number of abstractions directly
    const num_abstractions = abstractions.length;
    emitGraphStatus("AnalyzeRelationships", 10, `Analyzing relationships between ${num_abstractions} abstractions`);

    // Create context with abstraction names, indices, descriptions, and relevant file snippets
    let context = "Identified Abstractions:\n";
    const all_relevant_indices = new Set<number>();
    const abstraction_info_for_prompt: string[] = [];

    emitGraphStatus("AnalyzeRelationships", 20, "Preparing abstraction context");

    abstractions.forEach((abstr, i) => {
      // Use 'files' which contains indices directly
      const file_indices_str = abstr.files.map(String).join(", ");
      // Abstraction name and description might be translated already
      const info_line = `- Index ${i}: ${abstr.name} (Relevant file indices: [${file_indices_str}])\n  Description: ${abstr.description}`;
      context += info_line + "\n";
      abstraction_info_for_prompt.push(`${i} # ${abstr.name}`);
      abstr.files.forEach(idx => all_relevant_indices.add(idx));
    });

    emitGraphStatus("AnalyzeRelationships", 30, "Gathering relevant file snippets");

    context += "\nRelevant File Snippets (Referenced by Index and Path):\n";
    // Get content for relevant files using helper
    const relevant_files_content_map = getContentForIndices(
      files_data,
      Array.from(all_relevant_indices).sort()
    );

    // Format file content for context
    const fileContextStr = Object.entries(relevant_files_content_map)
      .map(([idxPath, content]) => `--- File: ${idxPath} ---\n${content}`)
      .join("\n\n");
    context += fileContextStr;

    emitGraphStatus("AnalyzeRelationships", 40, "Preparation complete, ready for relationship analysis");

    return {
      context,
      abstraction_listing: abstraction_info_for_prompt.join("\n"),
      num_abstractions,
      project_name,
      language,
      use_cache,
      user_id: shared.user_id,
      session_id: shared.session_id,
      tutorial_id: shared.tutorial_id
    };
  }

  async exec(prepRes: any): Promise<{
    summary: string;
    details: Array<{ from: number; to: number; label: string }>;
  }> {
    const {
      context,
      abstraction_listing,
      num_abstractions,
      project_name,
      language,
      use_cache,
      user_id,
      tutorial_id
    } = prepRes;

    emitGraphStatus("AnalyzeRelationships", 50, "Starting LLM analysis to identify relationships");
    console.log("Analyzing relationships using LLM...");

    // Add language instruction and hints only if not English
    let languageInstruction = "";
    let langHint = "";
    let listLangNote = "";

    if (language.toLowerCase() !== "english") {
      const capLang = language.charAt(0).toUpperCase() + language.slice(1);
      languageInstruction = `IMPORTANT: Generate the \`summary\` and relationship \`label\` fields in **${capLang}** language. Do NOT use English for these fields.\n\n`;
      langHint = ` (in ${capLang})`;
      listLangNote = ` (Names might be in ${capLang})`;
    }

    const prompt = buildPrompt(ANALYZE_RELATIONSHIPS_PROMPT, {
      project_name,
      list_lang_note: listLangNote,
      abstraction_listing,
      context,
      language_instruction: languageInstruction,
      lang_hint: langHint
    });

    emitGraphStatus("AnalyzeRelationships", 60, "Sending request to LLM for relationship analysis");
    const response = await callLlm_openrouter ({tutorial_id,
      prompt, use_cache: use_cache && this.curRetry === 0 , user_id });
    //const response = await callLlm(prompt, use_cache && this.curRetry === 0);
    emitGraphStatus("AnalyzeRelationships", 70, "Received response from LLM, processing results");

    // --- Validation ---
    const yamlMatch = response.trim().match(/```yaml([\s\S]*?)```/);
    if (!yamlMatch) {
      emitGraphStatus("AnalyzeRelationships", 75, "Error: LLM output does not contain YAML block");
      throw new Error('LLM output does not contain YAML block');
    }

    const yamlStr = yamlMatch[1].trim();
    emitGraphStatus("AnalyzeRelationships", 80, "Parsing YAML response");
    const relationshipsData = yaml.load(yamlStr) as any;

    if (!relationshipsData || typeof relationshipsData !== 'object' ||
        !('summary' in relationshipsData) || !('relationships' in relationshipsData)) {
      emitGraphStatus("AnalyzeRelationships", 82, "Error: LLM output is missing required keys");
      throw new Error("LLM output is not a dict or missing keys ('summary', 'relationships')");
    }

    if (typeof relationshipsData.summary !== 'string') {
      emitGraphStatus("AnalyzeRelationships", 83, "Error: summary is not a string");
      throw new Error("summary is not a string");
    }

    if (!Array.isArray(relationshipsData.relationships)) {
      emitGraphStatus("AnalyzeRelationships", 84, "Error: relationships is not a list");
      throw new Error("relationships is not a list");
    }

    // Validate relationships structure
    emitGraphStatus("AnalyzeRelationships", 85, "Validating relationship structure");
    const validatedRelationships: Array<{ from: number; to: number; label: string }> = [];

    for (const rel of relationshipsData.relationships) {
      // Check for required keys
      if (!rel || typeof rel !== 'object' ||
          !('from_abstraction' in rel) ||
          !('to_abstraction' in rel) ||
          !('label' in rel)) {
        emitGraphStatus("AnalyzeRelationships", 86, `Error: Missing keys in relationship item: ${JSON.stringify(rel)}`);
        throw new Error(
          `Missing keys (expected from_abstraction, to_abstraction, label) in relationship item: ${JSON.stringify(rel)}`
        );
      }

      // Validate 'label' is a string
      if (typeof rel.label !== 'string') {
        emitGraphStatus("AnalyzeRelationships", 87, `Error: Relationship label is not a string: ${JSON.stringify(rel)}`);
        throw new Error(`Relationship label is not a string: ${JSON.stringify(rel)}`);
      }

      // Validate indices
      try {
        const fromIdx = parseInt(String(rel.from_abstraction).split('#')[0].trim());
        const toIdx = parseInt(String(rel.to_abstraction).split('#')[0].trim());

        if (!(0 <= fromIdx && fromIdx < num_abstractions) ||
            !(0 <= toIdx && toIdx < num_abstractions)) {
          emitGraphStatus("AnalyzeRelationships", 88, `Error: Invalid index in relationship: from=${fromIdx}, to=${toIdx}`);
          throw new Error(
            `Invalid index in relationship: from=${fromIdx}, to=${toIdx}. Max index is ${num_abstractions-1}.`
          );
        }

        validatedRelationships.push({
          from: fromIdx,
          to: toIdx,
          label: rel.label // Potentially translated label
        });
      } catch (error) {
        emitGraphStatus("AnalyzeRelationships", 89, `Error: Could not parse indices from relationship: ${JSON.stringify(rel)}`);
        throw new Error(`Could not parse indices from relationship: ${JSON.stringify(rel)}`);
      }
    }

    emitGraphStatus("AnalyzeRelationships", 90, `Successfully validated ${validatedRelationships.length} relationships`);
    console.log("Generated project summary and relationship details.");

    return {
      summary: relationshipsData.summary, // Potentially translated summary
      details: validatedRelationships // Store validated, index-based relationships with potentially translated labels
    };
  }

  async post(
    shared: SharedStore,
    _: any,
    execRes: { summary: string; details: Array<{ from: number; to: number; label: string }> }
  ): Promise<string | undefined> {
    // Structure is now {"summary": str, "details": [{"from": int, "to": int, "label": str}]}
    // Summary and label might be translated
    emitGraphStatus("AnalyzeRelationships", 95, "Storing relationship data in shared store");
    shared.relationships = execRes;
    console.log("Stored relationships in shared store.", execRes);

    // Emit progress event
    emitProgress("Relationship Analysis", 60, `Identified ${execRes.details.length} relationships between abstractions`);

    // Final graph status
    emitGraphStatus("AnalyzeRelationships", 100, "Relationship analysis complete");

    return 'default';
  }
}