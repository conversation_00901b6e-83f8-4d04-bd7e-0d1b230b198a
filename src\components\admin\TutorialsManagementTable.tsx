
import React, { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { supabase } from "@/integrations/supabase/client";
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Trash2, <PERSON>, <PERSON>O<PERSON>, <PERSON>ader2, <PERSON>, <PERSON> } from "lucide-react";
import { toast } from "@/hooks/use-toast";
import { Database } from "@/integrations/supabase/types";

type TutorialRow = Database["public"]["Tables"]["tutorial_metadata"]["Row"];
type ProgrammingLanguage = Database["public"]["Enums"]["programming_language"];

const TutorialsManagementTable = () => {
  const queryClient = useQueryClient();
  const [deletingTutorial, setDeletingTutorial] = useState<string | null>(null);

  // Fetch all tutorials
  const { data: tutorials, isLoading, error } = useQuery({
    queryKey: ['admin-tutorials'],
    queryFn: async () => {
      const { data, error } = await supabase
        .from('tutorial_metadata')
        .select('*')
        .order('created_at', { ascending: false });
      
      if (error) throw error;
      return data;
    },
  });

  // Mutation for updating tutorial featured status
  const updateFeaturedMutation = useMutation({
    mutationFn: async ({ id, featured }: { id: string; featured: boolean }) => {
      const { error } = await supabase
        .from('tutorial_metadata')
        .update({ featured })
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tutorials'] });
      toast({
        title: "Success",
        description: "Tutorial featured status updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update featured status: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Mutation for updating programming language
  const updateProgrammingLanguageMutation = useMutation({
    mutationFn: async ({ id, programmingLanguage }: { id: string; programmingLanguage: ProgrammingLanguage }) => {
      const { error } = await supabase
        .from('tutorial_metadata')
        .update({ programming_language: programmingLanguage })
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tutorials'] });
      toast({
        title: "Success",
        description: "Programming language updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update programming language: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Mutation for updating public status
  const updatePublicStatusMutation = useMutation({
    mutationFn: async ({ id, isPublic }: { id: string; isPublic: boolean }) => {
      // If making private, also reset featured to false
      const updateData: { is_public: boolean; featured?: boolean } = { is_public: isPublic };
      if (!isPublic) {
        updateData.featured = false;
      }

      const { error } = await supabase
        .from('tutorial_metadata')
        .update(updateData)
        .eq('id', id);
      
      if (error) throw error;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tutorials'] });
      toast({
        title: "Success",
        description: "Public status updated successfully",
      });
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to update public status: ${error.message}`,
        variant: "destructive",
      });
    },
  });

  // Mutation for deleting tutorial
  const deleteTutorialMutation = useMutation({
    mutationFn: async (tutorialId: string) => {
      setDeletingTutorial(tutorialId);
      
      // First, get the tutorial metadata to access file URLs
      const { data: tutorialData, error: fetchError } = await supabase
        .from("tutorial_metadata")
        .select("*")
        .eq("id", tutorialId)
        .single();

      if (fetchError) {
        throw new Error(`Failed to fetch tutorial data: ${fetchError.message}`);
      }

      if (!tutorialData) {
        throw new Error("Tutorial not found");
      }

      // Delete files from storage
      const filesToDelete: string[] = [];
      const tutorialFolderPath = tutorialData.tutorial_id;

      // Add index file
      if (tutorialData.index_url) {
        filesToDelete.push(`${tutorialFolderPath}/index.md`);
      }

      // Add chapter files
      if (tutorialData.chapter_urls && Array.isArray(tutorialData.chapter_urls)) {
        tutorialData.chapter_urls.forEach((chapter: any) => {
          if (chapter.filename) {
            filesToDelete.push(`${tutorialFolderPath}/${chapter.filename}`);
          }
        });
      }

      // Delete files from storage if there are any to delete
      if (filesToDelete.length > 0) {
        const { error: storageError } = await supabase.storage
          .from('tutorials')
          .remove(filesToDelete);

        if (storageError) {
          console.error("Error deleting files from storage:", storageError);
          // Continue with deletion of metadata even if file deletion fails
        }
      }

      // Delete the tutorial metadata
      const { error: deleteError } = await supabase
        .from("tutorial_metadata")
        .delete()
        .eq("id", tutorialId);

      if (deleteError) {
        throw new Error(`Failed to delete tutorial: ${deleteError.message}`);
      }
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['admin-tutorials'] });
      toast({
        title: "Tutorial deleted",
        description: "The tutorial and all associated files have been successfully deleted.",
      });
      setDeletingTutorial(null);
    },
    onError: (error) => {
      toast({
        title: "Error",
        description: `Failed to delete tutorial: ${error.message}`,
        variant: "destructive",
      });
      setDeletingTutorial(null);
    },
  });

  const handleFeaturedToggle = (tutorial: TutorialRow) => {
    // Only allow featuring public tutorials
    if (!tutorial.is_public && !tutorial.featured) {
      toast({
        title: "Cannot feature private tutorial",
        description: "Only public tutorials can be featured. Please make this tutorial public first.",
        variant: "destructive",
      });
      return;
    }

    updateFeaturedMutation.mutate({
      id: tutorial.id,
      featured: !tutorial.featured,
    });
  };

  const handleProgrammingLanguageChange = (tutorialId: string, newLanguage: string) => {
    updateProgrammingLanguageMutation.mutate({
      id: tutorialId,
      programmingLanguage: newLanguage as ProgrammingLanguage,
    });
  };

  const handlePublicStatusToggle = (tutorial: TutorialRow) => {
    updatePublicStatusMutation.mutate({
      id: tutorial.id,
      isPublic: !tutorial.is_public,
    });
  };

  const handleDeleteTutorial = (tutorialId: string) => {
    deleteTutorialMutation.mutate(tutorialId);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  const programmingLanguages: ProgrammingLanguage[] = [
    'javascript', 'typescript', 'python', 'java', 'csharp', 'cpp', 'c', 'go', 'rust', 
    'php', 'ruby', 'swift', 'kotlin', 'dart', 'scala', 'haskell', 'clojure', 'elixir', 
    'erlang', 'lua', 'perl', 'r', 'matlab', 'shell', 'sql', 'html', 'css', 'other'
  ];

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="text-red-600">Error Loading Tutorials</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-red-600">Failed to load tutorials: {error.message}</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Eye className="h-5 w-5" />
          Tutorials Management
        </CardTitle>
      </CardHeader>
      <CardContent>
        {isLoading ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Loading tutorials...</span>
          </div>
        ) : !tutorials || tutorials.length === 0 ? (
          <p className="text-gray-500 text-center py-8">No tutorials found</p>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>User ID</TableHead>
                  <TableHead>Language</TableHead>
                  <TableHead>Programming Language</TableHead>
                  <TableHead>Views</TableHead>
                  <TableHead>Featured</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Public</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {tutorials.map((tutorial) => (
                  <TableRow key={tutorial.id}>
                    <TableCell className="font-medium max-w-xs">
                      <div className="truncate" title={tutorial.project_name}>
                        {tutorial.project_name}
                      </div>
                      {tutorial.description && (
                        <div className="text-xs text-gray-500 truncate" title={tutorial.description}>
                          {tutorial.description}
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="font-mono text-xs">
                      {tutorial.user_id?.slice(0, 8)}...
                    </TableCell>
                    <TableCell>{tutorial.language || 'N/A'}</TableCell>
                    <TableCell>
                      <Select
                        value={tutorial.programming_language || 'other'}
                        onValueChange={(value) => handleProgrammingLanguageChange(tutorial.id, value)}
                        disabled={updateProgrammingLanguageMutation.isPending}
                      >
                        <SelectTrigger className="w-32">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {programmingLanguages.map((lang) => (
                            <SelectItem key={lang} value={lang}>
                              {lang.charAt(0).toUpperCase() + lang.slice(1)}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </TableCell>
                    <TableCell>{tutorial.views || 0}</TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleFeaturedToggle(tutorial)}
                          disabled={updateFeaturedMutation.isPending || (!tutorial.is_public && !tutorial.featured)}
                          className={tutorial.featured ? "text-yellow-600" : "text-gray-400"}
                          title={!tutorial.is_public && !tutorial.featured ? "Only public tutorials can be featured" : ""}
                        >
                          {tutorial.featured ? <Star className="h-4 w-4 fill-current" /> : <StarOff className="h-4 w-4" />}
                        </Button>
                        {!tutorial.is_public && !tutorial.featured && (
                          <span className="text-xs text-gray-500">(Public only)</span>
                        )}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1 text-sm">
                        <Calendar className="h-3 w-3" />
                        {formatDate(tutorial.created_at)}
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Switch
                          checked={tutorial.is_public}
                          onCheckedChange={() => handlePublicStatusToggle(tutorial)}
                          disabled={updatePublicStatusMutation.isPending}
                        />
                        <span className={`text-xs ${
                          tutorial.is_public ? 'text-green-700' : 'text-gray-700'
                        }`}>
                          {tutorial.is_public ? 'Public' : 'Private'}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <AlertDialog>
                        <AlertDialogTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            className="text-red-600 hover:text-red-700"
                            disabled={deletingTutorial === tutorial.id}
                          >
                            {deletingTutorial === tutorial.id ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        </AlertDialogTrigger>
                        <AlertDialogContent>
                          <AlertDialogHeader>
                            <AlertDialogTitle>Delete Tutorial</AlertDialogTitle>
                            <AlertDialogDescription>
                              Are you sure you want to delete "{tutorial.project_name}"? 
                              This will permanently delete the tutorial and all associated files from storage. 
                              This action cannot be undone.
                            </AlertDialogDescription>
                          </AlertDialogHeader>
                          <AlertDialogFooter>
                            <AlertDialogCancel>Cancel</AlertDialogCancel>
                            <AlertDialogAction
                              onClick={() => handleDeleteTutorial(tutorial.id)}
                              className="bg-red-600 hover:bg-red-700"
                            >
                              Delete Tutorial
                            </AlertDialogAction>
                          </AlertDialogFooter>
                        </AlertDialogContent>
                      </AlertDialog>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default TutorialsManagementTable;
