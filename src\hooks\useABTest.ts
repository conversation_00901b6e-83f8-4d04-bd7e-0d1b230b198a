import { useState, useEffect, useCallback } from 'react';
import { useABTestStore } from '@/stores/useABTestStore';
import { abTestingService } from '@/services/abTesting';
import { useAuth } from '@/hooks/useAuth';
import type { UseABTestReturn, ABTestVariant } from '@/types/abTesting';

/**
 * Hook for A/B testing integration
 * 
 * @param experimentId - The ID of the experiment
 * @param defaultConfig - Default configuration to use if experiment is not active
 * @returns Object containing variant, config, loading state, and tracking functions
 */
export function useABTest<T = any>(
  experimentId: string,
  defaultConfig: T
): UseABTestReturn<T> {
  const { user } = useAuth();
  const {
    getVariant,
    trackEvent: storeTrackEvent,
    trackConversion: storeTrackConversion
  } = useABTestStore();
  
  const [variant, setVariant] = useState<ABTestVariant | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [config, setConfig] = useState<T>(defaultConfig);

  // Set user ID in service when user changes
  useEffect(() => {
    if (user?.id) {
      abTestingService.setUserId(user.id);
    }
  }, [user?.id]);

  // Load variant on mount and when experimentId changes
  useEffect(() => {
    let isMounted = true;

    const loadVariant = async () => {
      setIsLoading(true);
      
      try {
        const assignedVariant = await getVariant(experimentId);
        
        if (isMounted) {
          setVariant(assignedVariant);
          
          // Use variant config if available, otherwise use default
          if (assignedVariant?.config) {
            setConfig({ ...defaultConfig, ...assignedVariant.config });
          } else {
            setConfig(defaultConfig);
          }
        }
      } catch (error) {
        console.error('Error loading A/B test variant:', error);
        if (isMounted) {
          setConfig(defaultConfig);
        }
      } finally {
        if (isMounted) {
          setIsLoading(false);
        }
      }
    };

    loadVariant();

    return () => {
      isMounted = false;
    };
  }, [experimentId, getVariant, defaultConfig]);

  // Track custom event
  const trackEvent = useCallback(
    async (eventType: string, eventData?: Record<string, any>) => {
      try {
        await storeTrackEvent(experimentId, eventType, eventData);
      } catch (error) {
        console.error('Error tracking A/B test event:', error);
      }
    },
    [experimentId, storeTrackEvent]
  );

  // Track conversion
  const trackConversion = useCallback(
    async (conversionGoal?: string) => {
      try {
        await storeTrackConversion(experimentId, conversionGoal);
      } catch (error) {
        console.error('Error tracking A/B test conversion:', error);
      }
    },
    [experimentId, storeTrackConversion]
  );

  return {
    variant,
    config,
    isLoading,
    trackEvent,
    trackConversion
  };
}

/**
 * Hook for simple A/B testing with predefined variants
 * 
 * @param experimentId - The ID of the experiment
 * @param variants - Object mapping variant IDs to their configurations
 * @param defaultVariantId - ID of the default variant to use
 * @returns Object containing current variant config and tracking functions
 */
export function useSimpleABTest<T = any>(
  experimentId: string,
  variants: Record<string, T>,
  defaultVariantId: string
): UseABTestReturn<T> {
  const variantIds = Object.keys(variants);
  const defaultConfig = variants[defaultVariantId] || variants[variantIds[0]];
  
  const { variant, isLoading, trackEvent, trackConversion } = useABTest(
    experimentId,
    defaultConfig
  );

  // Get config for current variant
  const config = variant?.id && variants[variant.id] 
    ? variants[variant.id] 
    : defaultConfig;

  return {
    variant,
    config,
    isLoading,
    trackEvent,
    trackConversion
  };
}

/**
 * Hook for feature flag style A/B testing
 * 
 * @param experimentId - The ID of the experiment
 * @param feature - The feature to test (boolean)
 * @returns Object containing feature flag state and tracking functions
 */
export function useFeatureFlag(
  experimentId: string,
  defaultEnabled: boolean = false
): UseABTestReturn<{ enabled: boolean }> {
  const variants = {
    control: { enabled: false },
    treatment: { enabled: true }
  };
  
  const defaultVariantId = defaultEnabled ? 'treatment' : 'control';
  
  return useSimpleABTest(experimentId, variants, defaultVariantId);
}
