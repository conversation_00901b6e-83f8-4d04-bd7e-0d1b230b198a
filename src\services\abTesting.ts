import { supabase } from '@/integrations/supabase/client';
import type { 
  ABTestExperiment, 
  ABTestVariant, 
  ABTestAssignment, 
  ABTestEvent,
  ABTestResults 
} from '@/types/abTesting';

class ABTestingService {
  private sessionId: string;
  private userId?: string;

  constructor() {
    this.sessionId = this.getOrCreateSessionId();
  }

  setUserId(userId: string) {
    this.userId = userId;
  }

  private getOrCreateSessionId(): string {
    let sessionId = localStorage.getItem('ab_session_id');
    if (!sessionId) {
      sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      localStorage.setItem('ab_session_id', sessionId);
    }
    return sessionId;
  }

  /**
   * Get an active experiment by ID
   */
  async getExperiment(experimentId: string): Promise<ABTestExperiment | null> {
    try {
      const { data, error } = await supabase
        .from('ab_experiments')
        .select('*')
        .eq('id', experimentId)
        .eq('status', 'active')
        .single();

      if (error || !data) return null;

      return {
        ...data,
        startDate: data.start_date ? new Date(data.start_date) : undefined,
        endDate: data.end_date ? new Date(data.end_date) : undefined,
        variants: data.variants || [],
        targetAudience: data.target_audience || {},
        conversionGoals: data.conversion_goals || [],
        metadata: data.metadata || {}
      };
    } catch (error) {
      console.error('Error fetching experiment:', error);
      return null;
    }
  }

  /**
   * Get user's assignment for an experiment
   */
  async getAssignment(experimentId: string): Promise<ABTestAssignment | null> {
    try {
      const { data, error } = await supabase
        .from('ab_assignments')
        .select('*')
        .eq('experiment_id', experimentId)
        .eq('session_id', this.sessionId)
        .single();

      if (error || !data) return null;

      return {
        userId: data.user_id,
        sessionId: data.session_id,
        experimentId: data.experiment_id,
        variantId: data.variant_id,
        assignedAt: new Date(data.assigned_at),
        isConverted: data.is_converted,
        conversionEvents: data.conversion_events || []
      };
    } catch (error) {
      console.error('Error fetching assignment:', error);
      return null;
    }
  }

  /**
   * Assign user to a variant using weighted random selection
   */
  private selectVariant(variants: ABTestVariant[]): ABTestVariant {
    const totalWeight = variants.reduce((sum, variant) => sum + variant.weight, 0);
    const random = Math.random() * totalWeight;
    
    let currentWeight = 0;
    for (const variant of variants) {
      currentWeight += variant.weight;
      if (random <= currentWeight) {
        return variant;
      }
    }
    
    // Fallback to first variant
    return variants[0];
  }

  /**
   * Get or create assignment for an experiment
   */
  async getOrCreateAssignment(experimentId: string): Promise<ABTestAssignment | null> {
    // Check for existing assignment
    let assignment = await this.getAssignment(experimentId);
    if (assignment) return assignment;

    // Get experiment details
    const experiment = await this.getExperiment(experimentId);
    if (!experiment || experiment.variants.length === 0) return null;

    // Check if user should be included in experiment
    if (!this.shouldIncludeUser(experiment)) return null;

    // Select variant
    const selectedVariant = this.selectVariant(experiment.variants);

    // Create new assignment
    try {
      const newAssignment: Partial<ABTestAssignment> = {
        userId: this.userId,
        sessionId: this.sessionId,
        experimentId,
        variantId: selectedVariant.id,
        assignedAt: new Date(),
        isConverted: false,
        conversionEvents: []
      };

      const { data, error } = await supabase
        .from('ab_assignments')
        .insert({
          user_id: newAssignment.userId,
          session_id: newAssignment.sessionId,
          experiment_id: newAssignment.experimentId,
          variant_id: newAssignment.variantId,
          assigned_at: newAssignment.assignedAt?.toISOString(),
          is_converted: newAssignment.isConverted,
          conversion_events: newAssignment.conversionEvents
        })
        .select()
        .single();

      if (error) {
        console.error('Error creating assignment:', error);
        return null;
      }

      return newAssignment as ABTestAssignment;
    } catch (error) {
      console.error('Error creating assignment:', error);
      return null;
    }
  }

  /**
   * Check if user should be included in experiment based on targeting rules
   */
  private shouldIncludeUser(experiment: ABTestExperiment): boolean {
    const { targetAudience } = experiment;
    
    if (!targetAudience) return true;

    // Check percentage targeting
    if (targetAudience.percentage && targetAudience.percentage < 100) {
      const hash = this.hashString(this.sessionId + experiment.id);
      const userPercentile = (hash % 100) + 1;
      if (userPercentile > targetAudience.percentage) return false;
    }

    // Additional targeting rules can be added here
    // e.g., user type, geographic location, etc.

    return true;
  }

  /**
   * Simple hash function for consistent user bucketing
   */
  private hashString(str: string): number {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
  }

  /**
   * Track an event for an experiment
   */
  async trackEvent(
    experimentId: string,
    variantId: string,
    eventType: 'view' | 'click' | 'conversion' | 'custom',
    eventName?: string,
    eventData?: Record<string, any>
  ): Promise<void> {
    try {
      const event: Partial<ABTestEvent> = {
        experimentId,
        variantId,
        userId: this.userId,
        sessionId: this.sessionId,
        eventType,
        eventName,
        eventData,
        timestamp: new Date()
      };

      await supabase
        .from('ab_events')
        .insert({
          experiment_id: event.experimentId,
          variant_id: event.variantId,
          user_id: event.userId,
          session_id: event.sessionId,
          event_type: event.eventType,
          event_name: event.eventName,
          event_data: event.eventData,
          timestamp: event.timestamp?.toISOString()
        });

    } catch (error) {
      console.error('Error tracking event:', error);
    }
  }

  /**
   * Track a conversion for an experiment
   */
  async trackConversion(
    experimentId: string,
    variantId: string,
    conversionGoal?: string
  ): Promise<void> {
    try {
      // Track conversion event
      await this.trackEvent(experimentId, variantId, 'conversion', conversionGoal);

      // Update assignment conversion status
      await supabase
        .from('ab_assignments')
        .update({
          is_converted: true,
          conversion_events: supabase.rpc('array_append', {
            arr: 'conversion_events',
            elem: conversionGoal || 'default'
          })
        })
        .eq('experiment_id', experimentId)
        .eq('session_id', this.sessionId);

    } catch (error) {
      console.error('Error tracking conversion:', error);
    }
  }

  /**
   * Get experiment results and statistics
   */
  async getExperimentResults(experimentId: string): Promise<ABTestResults | null> {
    try {
      const { data, error } = await supabase
        .rpc('get_ab_experiment_results', { experiment_id: experimentId });

      if (error || !data) return null;

      return data as ABTestResults;
    } catch (error) {
      console.error('Error fetching experiment results:', error);
      return null;
    }
  }
}

export const abTestingService = new ABTestingService();
