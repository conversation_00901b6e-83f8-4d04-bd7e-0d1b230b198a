
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";
import {
  BookOpen,
  Search,
  Plus,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import TutorialCard from "@/components/TutorialCard";
import {
  useFeaturedTutorials,
  useRecentTutorials,
  usePopularTutorials,
  Tutorial,
} from "@/hooks/useTutorials";

const PublicGallery = () => {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [selectedDifficulty, setSelectedDifficulty] = useState("");

  const {
    tutorials: featuredTutorials,
    loading: featuredLoading,
    error: featuredError,
  } = useFeaturedTutorials();
  const {
    tutorials: recentTutorials,
    loading: recentLoading,
    error: recentError,
  } = useRecentTutorials();
  const {
    tutorials: popularTutorials,
    loading: popularLoading,
    error: popularError,
  } = usePopularTutorials();

  // Deduplicate tutorials and combine their types
  const deduplicatedTutorials = React.useMemo(() => {
    const tutorialMap = new Map<string, Tutorial & { types: string[] }>();

    // Add featured tutorials
    featuredTutorials.forEach((tutorial) => {
      const existing = tutorialMap.get(tutorial.id);
      if (existing) {
        existing.types.push("featured");
      } else {
        tutorialMap.set(tutorial.id, { ...tutorial, types: ["featured"] });
      }
    });

    // Add popular tutorials
    popularTutorials.forEach((tutorial) => {
      const existing = tutorialMap.get(tutorial.id);
      if (existing) {
        existing.types.push("popular");
      } else {
        tutorialMap.set(tutorial.id, { ...tutorial, types: ["popular"] });
      }
    });

    // Add recent tutorials
    recentTutorials.forEach((tutorial) => {
      const existing = tutorialMap.get(tutorial.id);
      if (existing) {
        existing.types.push("recent");
      } else {
        tutorialMap.set(tutorial.id, { ...tutorial, types: ["recent"] });
      }
    });

    return Array.from(tutorialMap.values());
  }, [featuredTutorials, popularTutorials, recentTutorials]);

  //console.log("deduplicatedTutorials:", deduplicatedTutorials);

  // Filter tutorials based on search and filters
  const filteredTutorials = deduplicatedTutorials.filter((tutorial) => {
    const matchesSearch =
      tutorial.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      tutorial.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLanguage =
      !selectedLanguage || tutorial.language === selectedLanguage;
    const matchesDifficulty =
      !selectedDifficulty || tutorial.difficulty === selectedDifficulty;

    return matchesSearch && matchesLanguage && matchesDifficulty;
  });

  const isLoading = featuredLoading || recentLoading || popularLoading;
  const hasError = featuredError || recentError || popularError;

  return (
    <div className="container mx-auto px-4 py-6">
    
          {/* Page Title */}
          <div id="page-title" className="mb-8">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">
              Community Gallery
            </h1>
            <p className="text-gray-600 max-w-3xl">
              Browse through a collection of AI-generated tutorials from GitHub
              repositories. Find comprehensive guides for popular codebases and
              frameworks.
            </p>
          </div>
    
          {/* Search and Filter Section */}
          <div
            id="search-filter-section"
            className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8"
          >
            <div className="flex flex-col md:flex-row gap-4">
              {/* Search Bar */}
              <div className="w-full md:w-1/2">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-4 w-4 text-gray-400" />
                  </div>
                  <Input
                    type="text"
                    className="pl-10 pr-4"
                    placeholder="Search tutorials by name, language, or framework..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
    
              {/* Filters */}
              <div className="w-full md:w-1/2 flex flex-wrap gap-3">
                <select 
                  className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500"
                  value={selectedLanguage}
                  onChange={(e) => setSelectedLanguage(e.target.value)}
                >
                  <option value="">All Languages</option>
                  <option value="JavaScript">JavaScript</option>
                  <option value="TypeScript">TypeScript</option>
                  <option value="Python">Python</option>
                  <option value="Java">Java</option>
                  <option value="Go">Go</option>
                  <option value="Rust">Rust</option>
                </select>
    
                <select 
                  className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500"
                  value={selectedDifficulty}
                  onChange={(e) => setSelectedDifficulty(e.target.value)}
                >
                  <option value="">All Difficulties</option>
                  <option value="Beginner">Beginner</option>
                  <option value="Intermediate">Intermediate</option>
                  <option value="Advanced">Advanced</option>
                </select>
    
                <select className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500">
                  <option value="newest">Newest First</option>
                  <option value="oldest">Oldest First</option>
                  <option value="popular">Most Popular</option>
                </select>
              </div>
            </div>
          </div>
    

      {/* Gallery Grid */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {Array.from({ length: 12 }).map((_, i) => (
            <div
              key={i}
              className="bg-white rounded-lg border border-gray-200 overflow-hidden animate-pulse"
            >
              <div className="h-[140px] bg-gray-200"></div>
              <div className="p-4 space-y-2">
                <div className="h-4 bg-gray-200 rounded"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
                <div className="flex gap-1">
                  <div className="h-5 bg-gray-200 rounded w-12"></div>
                  <div className="h-5 bg-gray-200 rounded w-16"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : hasError ? (
        <div className="text-center py-12">
          <div className="text-red-500 mb-4">Error loading tutorials</div>
          <Button onClick={() => window.location.reload()}>Try Again</Button>
        </div>
      ) : filteredTutorials.length === 0 ? (
        RenderEmpty()
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {filteredTutorials.map((tutorial) => (
            <TutorialCard
              key={tutorial.id}
              tutorial={{
                ...tutorial,
                // Determine the primary type for the card
                type: tutorial.types.includes("featured") 
                  ? "featured" 
                  : tutorial.types.includes("popular") 
                  ? "popular" 
                  : "recent",
                // Pass all types for multiple badges
                allTypes: tutorial.types
              }}
              showDeleteButton={false}
            />
          ))}
        </div>
      )}
    </div>
  );

  function RenderEmpty(): React.ReactNode {
    return <div className="text-center py-16">
      <BookOpen className="mx-auto h-16 w-16 text-gray-400 mb-4" />
      <h3 className="text-xl font-medium text-gray-900 mb-2">
        {searchTerm || selectedLanguage || selectedDifficulty
          ? "No matching tutorials found"
          : "No tutorials yet"}
      </h3>
      <p className="text-gray-600 mb-6">
        {searchTerm || selectedLanguage || selectedDifficulty
          ? "Try adjusting your search or filters"
          : "Be the first to create amazing tutorials from your repositories"}
      </p>
      <Link to="/auth">
        <Button className="bg-primary-600 hover:bg-primary-700">
          Create First Tutorial
        </Button>
      </Link>
    </div>;
  }
};

export default PublicGallery;
