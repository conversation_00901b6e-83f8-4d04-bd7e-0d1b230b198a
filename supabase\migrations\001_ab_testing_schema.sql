-- A/B Testing Tables for CodeTutorPro
-- Migration: 001_ab_testing_schema.sql

-- Table for storing experiment configurations
CREATE TABLE IF NOT EXISTS ab_experiments (
  id TEXT PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'active', 'paused', 'completed')),
  start_date TIMESTAMPTZ,
  end_date TIMESTAMPTZ,
  variants JSONB NOT NULL DEFAULT '[]',
  target_audience JSONB DEFAULT '{}',
  conversion_goals TEXT[] DEFAULT '{}',
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Table for storing user assignments to variants
CREATE TABLE IF NOT EXISTS ab_assignments (
  id BIGSERIAL PRIMARY KEY,
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE,
  session_id TEXT NOT NULL,
  experiment_id TEXT NOT NULL REFERENCES ab_experiments(id) ON DELETE CASCADE,
  variant_id TEXT NOT NULL,
  assigned_at TIMESTAMPTZ DEFAULT NOW(),
  is_converted BOOLEAN DEFAULT FALSE,
  conversion_events TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(session_id, experiment_id)
);

-- Table for storing events and analytics
CREATE TABLE IF NOT EXISTS ab_events (
  id BIGSERIAL PRIMARY KEY,
  experiment_id TEXT NOT NULL REFERENCES ab_experiments(id) ON DELETE CASCADE,
  variant_id TEXT NOT NULL,
  user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
  session_id TEXT NOT NULL,
  event_type TEXT NOT NULL CHECK (event_type IN ('view', 'click', 'conversion', 'custom')),
  event_name TEXT,
  event_data JSONB DEFAULT '{}',
  timestamp TIMESTAMPTZ DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX IF NOT EXISTS idx_ab_experiments_status ON ab_experiments(status);
CREATE INDEX IF NOT EXISTS idx_ab_experiments_dates ON ab_experiments(start_date, end_date);
CREATE INDEX IF NOT EXISTS idx_ab_assignments_experiment ON ab_assignments(experiment_id);
CREATE INDEX IF NOT EXISTS idx_ab_assignments_session ON ab_assignments(session_id);
CREATE INDEX IF NOT EXISTS idx_ab_assignments_user ON ab_assignments(user_id);
CREATE INDEX IF NOT EXISTS idx_ab_events_experiment ON ab_events(experiment_id);
CREATE INDEX IF NOT EXISTS idx_ab_events_variant ON ab_events(variant_id);
CREATE INDEX IF NOT EXISTS idx_ab_events_timestamp ON ab_events(timestamp);
CREATE INDEX IF NOT EXISTS idx_ab_events_type ON ab_events(event_type);

-- Function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Trigger to automatically update updated_at
CREATE TRIGGER update_ab_experiments_updated_at BEFORE UPDATE ON ab_experiments
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to get experiment results with statistics
CREATE OR REPLACE FUNCTION get_ab_experiment_results(experiment_id TEXT)
RETURNS JSONB AS $$
DECLARE
    result JSONB;
    variant_results JSONB[];
    variant_record RECORD;
    total_views INTEGER := 0;
    total_conversions INTEGER := 0;
BEGIN
    -- Get results for each variant
    FOR variant_record IN
        SELECT 
            a.variant_id,
            COUNT(*) as views,
            COUNT(CASE WHEN a.is_converted THEN 1 END) as conversions,
            ROUND(
                CASE 
                    WHEN COUNT(*) > 0 
                    THEN (COUNT(CASE WHEN a.is_converted THEN 1 END)::FLOAT / COUNT(*)::FLOAT) * 100 
                    ELSE 0 
                END, 2
            ) as conversion_rate
        FROM ab_assignments a
        WHERE a.experiment_id = get_ab_experiment_results.experiment_id
        GROUP BY a.variant_id
    LOOP
        variant_results := array_append(variant_results, jsonb_build_object(
            'variantId', variant_record.variant_id,
            'views', variant_record.views,
            'conversions', variant_record.conversions,
            'conversionRate', variant_record.conversion_rate
        ));
        
        total_views := total_views + variant_record.views;
        total_conversions := total_conversions + variant_record.conversions;
    END LOOP;

    -- Build final result
    result := jsonb_build_object(
        'experimentId', experiment_id,
        'variants', COALESCE(variant_results, ARRAY[]::JSONB[]),
        'totalViews', total_views,
        'totalConversions', total_conversions
    );

    RETURN result;
END;
$$ LANGUAGE plpgsql;

-- Row Level Security (RLS) policies
ALTER TABLE ab_experiments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE ab_events ENABLE ROW LEVEL SECURITY;

-- Policy for ab_experiments: Allow read access to all authenticated users
CREATE POLICY "Allow read access to experiments" ON ab_experiments
    FOR SELECT USING (auth.role() = 'authenticated');

-- Policy for ab_assignments: Users can only see their own assignments
CREATE POLICY "Users can view own assignments" ON ab_assignments
    FOR SELECT USING (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can insert own assignments" ON ab_assignments
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Users can update own assignments" ON ab_assignments
    FOR UPDATE USING (auth.uid() = user_id OR user_id IS NULL);

-- Policy for ab_events: Users can only insert their own events
CREATE POLICY "Users can insert own events" ON ab_events
    FOR INSERT WITH CHECK (auth.uid() = user_id OR user_id IS NULL);

CREATE POLICY "Allow read access to events for analytics" ON ab_events
    FOR SELECT USING (auth.role() = 'authenticated');

-- Admin policies (for users with admin role)
CREATE POLICY "Admins can manage experiments" ON ab_experiments
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

CREATE POLICY "Admins can view all assignments" ON ab_assignments
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );

CREATE POLICY "Admins can view all events" ON ab_events
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM auth.users 
            WHERE auth.users.id = auth.uid() 
            AND auth.users.raw_user_meta_data->>'role' = 'admin'
        )
    );
