
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Badge } from "@/components/ui/badge";
import { useQuery } from "@tanstack/react-query";
import { getTutorialUsageStats } from "@/utils/usageAnalytics";

const TutorialUsagePanel = () => {
  const { data: tutorialStats, isLoading } = useQuery({
    queryKey: ['tutorial-usage-stats'],
    queryFn: () => getTutorialUsageStats(),
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-6 w-40" />
          <Skeleton className="h-4 w-60" />
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex space-x-4">
                <Skeleton className="h-4 w-48" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!tutorialStats || Object.keys(tutorialStats).length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Tutorial Cost Breakdown</CardTitle>
          <CardDescription>Cost analysis by tutorial creation</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-center text-muted-foreground py-8">No tutorial usage data available</p>
        </CardContent>
      </Card>
    );
  }

  const formatCurrency = (amount: number) => 
    new Intl.NumberFormat('en-US', { 
      style: 'currency', 
      currency: 'USD',
      minimumFractionDigits: 2,
      maximumFractionDigits: 20
    }).format(amount);

  const sortedTutorials = Object.entries(tutorialStats).sort(
    ([, a], [, b]) => b.cost - a.cost
  );

  return (
    <Card>
      <CardHeader>
        <CardTitle>Tutorial Cost Breakdown</CardTitle>
        <CardDescription>
          LLM costs breakdown by tutorial creation ({sortedTutorials.length} tutorials)
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Tutorial ID</TableHead>
                <TableHead>API Calls</TableHead>
                <TableHead>Total Tokens</TableHead>
                <TableHead>Total Cost</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {sortedTutorials.map(([tutorialId, stats]) => (
                <TableRow key={tutorialId}>
                  <TableCell className="max-w-64">
                    <div className="flex flex-col">
                      <span className="font-medium truncate">{tutorialId}</span>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Badge variant="secondary">
                      {stats.calls}
                    </Badge>
                  </TableCell>
                  <TableCell className="font-mono">
                    {stats.tokens.toLocaleString()}
                  </TableCell>
                  <TableCell className="font-mono font-medium">
                    {formatCurrency(stats.cost)}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};

export default TutorialUsagePanel;
