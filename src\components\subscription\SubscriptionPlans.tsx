import React, { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/hooks/useAuth";
import { useToast } from "@/hooks/use-toast";
import { useNavigate } from "react-router-dom";
import { stripePriceIds } from "./stripe";
import { useSubscription } from "@/hooks/useSubscription";

interface TierPlan {
  id: string;
  tier_name: string;
  price_monthly: number;
  max_tutorials_per_month: number;
  max_file_size_mb: number;
  can_access_private_repos: boolean;
  has_priority_support: boolean;
  has_api_access: boolean;
  has_custom_branding: boolean;
  has_trial: boolean;
  trial_days: number;
  features: string[];
}

export const SubscriptionPlans = () => {
  const { session } = useAuth();
  const { toast } = useToast();
  const navigate = useNavigate();
  const [tierPlans, setTierPlans] = useState<TierPlan[]>([]);
  const [loading, setLoading] = useState(true);

  const {
    subscription_tier,

    loading: subscriptionLoading,
  } = useSubscription();

  useEffect(() => {
    fetchTierPlans();
  }, []);

  const fetchTierPlans = async () => {
    try {
      const { data, error } = await supabase
        .from("tier_settings")
        .select("*")
        .order("price_monthly");

      if (error) throw error;
      setTierPlans(data || []);
    } catch (error) {
      console.error("Error fetching tier plans:", error);
      toast({
        title: "Error",
        description: "Failed to load subscription plans",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleSubscribe = async (tierName: string) => {
    //console.log("session", session);

    if (session === undefined || session === null || !session?.access_token) {
      navigate("/auth");
      return;
    }

    const priceId = stripePriceIds[tierName as keyof typeof stripePriceIds];
    if (!priceId) {
      toast({
        title: "Error",
        description: "Invalid subscription plan selected",
        variant: "destructive",
      });
      return;
    }

    try {
      const { data, error } = await supabase.functions.invoke(
        "create-checkout",
        {
          body: { priceId, tierName },
          headers: {
            Authorization: `Bearer ${session.access_token}`,
          },
        }
      );

      if (error) throw error;

      // Open Stripe checkout in a new tab
      window.open(data.url, "_blank");
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast({
        title: "Error",
        description: "Failed to start checkout process",
        variant: "destructive",
      });
    }
  };

  const getFeaturesList = (plan: TierPlan) => {
    const baseFeatures = [];

    if (plan.max_tutorials_per_month === -1) {
      baseFeatures.push("Unlimited tutorials");
    } else {
      baseFeatures.push(`${plan.max_tutorials_per_month} tutorials per month`);
    }

    if (plan.can_access_private_repos) {
      baseFeatures.push("Private repositories");
    } else {
      baseFeatures.push("Public repositories");
    }

    if (plan.has_priority_support) {
      baseFeatures.push("Priority support");
    } else {
      baseFeatures.push("Email support");
    }

    if (plan.has_api_access) {
      baseFeatures.push("API access");
    }

    if (plan.has_custom_branding) {
      baseFeatures.push("Custom branding");
    }

    // Add custom features from the database
    if (plan.features && plan.features.length > 0) {
      baseFeatures.push(...plan.features);
    }

    return baseFeatures;
  };

  if (loading || subscriptionLoading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
        {[...Array(3)].map((_, index) => (
          <Card key={index} className="animate-pulse">
            <CardHeader className="text-center pb-4">
              <div className="h-6 bg-gray-200 rounded w-1/2 mx-auto mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mx-auto"></div>
              <div className="h-8 bg-gray-200 rounded w-1/3 mx-auto mt-4"></div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {[...Array(4)].map((_, i) => (
                  <div key={i} className="h-4 bg-gray-200 rounded"></div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 max-w-6xl mx-auto">
      {tierPlans.map((plan, index) => {
        const isPopular = plan.tier_name === "Propel";

        return (
          <Card
            key={plan.id}
            className={`border-2 transition-colors duration-300 flex flex-col ${
              isPopular
                ? "border-primary shadow-xl relative"
                : "border-gray-200 hover:border-primary"
            }`}
          >
            {isPopular && (
              <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
                <Badge className="bg-primary text-white">Most Popular</Badge>
              </div>
            )}
            <CardHeader className="text-center pb-4">
              <CardTitle className="text-2xl mb-2">{plan.tier_name}</CardTitle>
              <CardDescription className="text-base">
                {plan.tier_name === "Spark" && "Perfect for getting started"}
                {plan.tier_name === "Propel" && "Perfect for growing teams"}
                {plan.tier_name === "Apex" && "Perfect for enterprises"}
              </CardDescription>
              <div className="mt-4">
                {plan.tier_name === "Spark" ? (
                  <span className="text-4xl font-bold text-gray-800">Free</span>
                ) : (
                  <>
                    <span className="text-4xl font-bold text-gray-800">
                      ${plan.price_monthly.toFixed(2)}
                    </span>
                    <span className="text-gray-600">/month</span>
                  </>
                )}
              </div>
              {plan.has_trial && plan.trial_days > 0 && (
                <div className="mt-2">
                  <Badge
                    variant="outline"
                    className="text-green-600 border-green-600"
                  >
                    {plan.trial_days}-day free trial
                  </Badge>
                </div>
              )}
            </CardHeader>
            <CardContent className="flex-1 flex flex-col">
              <ul className="space-y-3 flex-1">
                {getFeaturesList(plan).map((feature, featureIndex) => (
                  <li key={featureIndex} className="flex items-center">
                    <i className="fa-solid fa-check text-green-500 mr-3"></i>
                    <span>{feature}</span>
                  </li>
                ))}
              </ul>
              <div className="mt-auto pt-6">
                <Button
                  className="w-full"
                  variant={isPopular ? "default" : "outline"}
                  onClick={() => handleSubscribe(plan.tier_name)}
                  disabled={plan.tier_name === subscription_tier}
                >
                  {plan.tier_name === subscription_tier
                    ? "Current Plan"
                    : `Subscribe to ${plan.tier_name}`}
                  {plan.has_trial &&
                    plan.trial_days > 0 &&
                    plan.tier_name !== "Spark" && (
                      <span className="ml-1">- Start Free Trial</span>
                    )}
                </Button>
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
};
