/**
 * Gets content for specified file indices
 * @param filesData Array of [path, content] tuples
 * @param indices Array of indices to retrieve
 * @returns Map of "index # path" to content
 */
export function getContentForIndices(
  filesData: Array<[string, string]>,
  indices: number[]
): Record<string, string> {
  const result: Record<string, string> = {};
  
  indices.forEach(idx => {
    if (idx >= 0 && idx < filesData.length) {
      const [path, content] = filesData[idx];
      result[`${idx} # ${path}`] = content;
    }
  });
  
  return result;
}