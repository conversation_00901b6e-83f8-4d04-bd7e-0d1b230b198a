import { useState, useEffect } from "react";
import { motion, useScroll, useTransform } from "framer-motion";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Star, Users, Shield, Zap, Clock, CheckCircle } from "lucide-react";
import JoinWaitingListModal from "./JoinWaitingListModal";
import { useABTest } from "@/hooks/useABTest";
import type { HeroHeadlineConfig } from "@/types/abTesting";

const HeroSection = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [userCount, setUserCount] = useState(0);
  const [timeLeft, setTimeLeft] = useState({ days: 0, hours: 0, minutes: 0 });
  const { scrollY } = useScroll();
  const y = useTransform(scrollY, [0, 500], [0, 150]);

  // A/B Test for hero headline
  const defaultConfig: HeroHeadlineConfig = {
    title: 'CodeTutorPro',
    subtitle: 'Transform any GitHub repository into AI-powered step-by-step tutorials for developers. Stop struggling with complex codebases and start learning efficiently.',
    ctaText: 'Get Early Access',
    emphasizedWords: ['GitHub repository', 'AI-powered step-by-step tutorials']
  };

  const { config: heroConfig, trackEvent, trackConversion } = useABTest<HeroHeadlineConfig>(
    'hero-headline-v1',
    defaultConfig
  );

  // Helper function to render text with emphasized words
  const renderTextWithEmphasis = (text: string, emphasizedWords: string[] = []) => {
    if (!emphasizedWords.length) return text;

    let result = text;
    emphasizedWords.forEach(word => {
      const regex = new RegExp(`(${word})`, 'gi');
      result = result.replace(regex, '<strong class="text-gray-900 font-semibold">$1</strong>');
    });

    return <span dangerouslySetInnerHTML={{ __html: result }} />;
  };

  // Handle CTA click with tracking
  const handleCTAClick = () => {
    trackEvent('cta_click');
    setIsModalOpen(true);
  };

  // Handle modal open with tracking
  const handleModalOpen = () => {
    trackEvent('modal_open');
    setIsModalOpen(true);
  };

  // Handle conversion tracking when modal opens
  useEffect(() => {
    if (isModalOpen) {
      trackConversion('waitlist_signup');
    }
  }, [isModalOpen, trackConversion]);

  // Animated counter for social proof
  useEffect(() => {
    const targetCount = 2847;
    const duration = 2000;
    const increment = targetCount / (duration / 16);
    let current = 0;

    const timer = setInterval(() => {
      current += increment;
      if (current >= targetCount) {
        setUserCount(targetCount);
        clearInterval(timer);
      } else {
        setUserCount(Math.floor(current));
      }
    }, 16);

    return () => clearInterval(timer);
  }, []);

  // Countdown timer for urgency
  useEffect(() => {
    const targetDate = new Date(Date.UTC(2025, 5, 30, 23, 59, 59, 999));
    //targetDate.setDate(targetDate.getDate() + 14); // 14 days from now

    const updateCountdown = () => {
      const now = new Date().getTime();
      const distance = targetDate.getTime() - now;

      setTimeLeft({
        days: Math.floor(distance / (1000 * 60 * 60 * 24)),
        hours: Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60)),
        minutes: Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60))
      });
    };

    updateCountdown();
    const interval = setInterval(updateCountdown, 60000);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative pb-20 pt-8 min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-white overflow-hidden">
      {/* Enhanced animated background elements */}
      <motion.div
        className="absolute inset-0"
        style={{ y }}
      >
        <motion.div
          className="absolute top-1/4 left-1/4 w-64 h-64 bg-gray-200/30 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.4, 0.2],
          }}
          transition={{ duration: 8, repeat: Infinity, ease: "easeInOut" }}
        />
        <motion.div
          className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gray-300/20 rounded-full blur-3xl"
          animate={{
            scale: [1.1, 1, 1.1],
            opacity: [0.3, 0.5, 0.3],
          }}
          transition={{ duration: 10, repeat: Infinity, ease: "easeInOut", delay: 1 }}
        />
        <motion.div
          className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-72 h-72 bg-gray-100/40 rounded-full blur-3xl"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.1, 0.3, 0.1],
          }}
          transition={{ duration: 12, repeat: Infinity, ease: "easeInOut", delay: 2 }}
        />

        {/* Floating particles - minimalistic */}
        {[...Array(8)].map((_, i) => (
          <motion.div
            key={i}
            className="absolute w-1 h-1 bg-gray-400/30 rounded-full"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [-20, -60, -20],
              opacity: [0, 0.6, 0],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </motion.div>

      <div className="relative z-10 text-center px-6 max-w-6xl mx-auto">
        {/* Minimalistic status badge */}
        <motion.div
          className="inline-flex items-center px-6 py-3 mb-6 bg-gray-100 backdrop-blur-sm rounded-full border border-gray-200"
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.6 }}
        >
          <Clock className="w-4 h-4 text-gray-600 mr-2" />
          <span className="text-gray-700 text-sm font-medium">
            Limited Early Access - Only {timeLeft.days} days left
          </span>
        </motion.div>

        {/* Social proof badge */}
        <motion.div
          className="ml-8 inline-flex items-center px-4 py-2 mb-8 bg-gray-50 backdrop-blur-sm rounded-full border border-gray-200"
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <Users className="w-4 h-4 text-gray-600 mr-2" />
          <span className="text-gray-700 text-sm font-medium">
            Join {userCount.toLocaleString()}+ developers already on the waitlist
          </span>
        </motion.div>

        {/* Main heading with A/B test variations */}
        <motion.h1
          className="text-5xl md:text-7xl lg:text-8xl font-bold text-gray-900 mb-6"
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.3 }}
        >
          <span className="sr-only">{heroConfig.title} - AI-Powered GitHub Repository Tutorial Generator</span>
          <span aria-hidden="true">
            {heroConfig.title === 'CodeTutorPro' ? (
              <>Code<span className="text-gray-600">Tutor</span>Pro</>
            ) : (
              heroConfig.title
            )}
          </span>
        </motion.h1>

        {/* Enhanced subheading with A/B test variations */}
        <motion.h2
          className="text-xl md:text-2xl lg:text-3xl text-gray-600 mb-6 max-w-4xl mx-auto leading-relaxed font-normal"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.5 }}
        >
          {renderTextWithEmphasis(heroConfig.subtitle, heroConfig.emphasizedWords)}
        </motion.h2>

        {/* Value proposition with benefits */}
        <motion.div
          className="flex flex-wrap justify-center gap-4 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.7 }}
        >
          {[
            "🚀 AI-Powered Analysis",
            "📚 Beginner-Friendly",
            "⚡ Instant Generation",
            "🔒 100% Free Early Access"
          ].map((benefit, index) => (
            <Badge key={index} variant="secondary" className="bg-gray-100 text-gray-700 border-gray-200 px-4 py-2">
              {benefit}
            </Badge>
          ))}
        </motion.div>

        {/* Enhanced description */}
        <motion.p
          className="text-lg text-gray-500 mb-12 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.9 }}
        >
          Powered by cutting-edge Agentic AI that understands code structure, dependencies, and learning patterns.
          Turn hours of code exploration into minutes of guided learning.
        </motion.p>

        {/* Enhanced CTA Section */}
        <motion.div
          className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-12"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.1 }}
        >
          {/* Primary CTA */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Button
              size="lg"
              className="bg-gray-900 hover:bg-gray-800 text-white px-10 py-4 text-xl font-semibold rounded-lg transition-all duration-300 shadow-lg hover:shadow-xl"
              onClick={handleCTAClick}
            >
              <Zap className="w-5 h-5 mr-2" />
              {heroConfig.ctaText}
            </Button>
          </motion.div>

          {/* Secondary CTA */}
          {/* <motion.div
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <Button
              variant="outline"
              size="lg"
              className="border-2 border-white/30 text-white hover:bg-white/10 px-8 py-4 text-lg font-semibold rounded-full transition-all duration-300"
              onClick={() => {
                document.querySelector('#features')?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              See How It Works
            </Button>
          </motion.div> */}
        </motion.div>

        {/* Trust signals - minimalistic */}
        <motion.div
          className="flex flex-col sm:flex-row gap-8 justify-center items-center text-sm text-gray-500 mb-12"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.3 }}
        >
          <div className="flex items-center gap-2">
            <CheckCircle className="w-4 h-4 text-gray-600" />
            <span>100% Free Early Access</span>
          </div>
          <div className="flex items-center gap-2">
            <Shield className="w-4 h-4 text-gray-600" />
            <span>No Credit Card Required</span>
          </div>
          <div className="flex items-center gap-2">
            <Star className="w-4 h-4 text-gray-600" />
            <span>Join {userCount.toLocaleString()}+ Developers</span>
          </div>
        </motion.div>

        {/* Countdown timer - minimalistic */}
        <motion.div
          className="bg-gray-50 backdrop-blur-sm rounded-2xl p-6 border border-gray-200 mb-12 max-w-md mx-auto"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.8, delay: 1.5 }}
        >
          <div className="text-gray-600 text-sm font-medium mb-2">⏰ Early Access Ends In:</div>
          <div className="flex justify-center gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{timeLeft.days.toString().padStart(2, '0')}</div>
              <div className="text-xs text-gray-500">Days</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{timeLeft.hours.toString().padStart(2, '0')}</div>
              <div className="text-xs text-gray-500">Hours</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-gray-900">{timeLeft.minutes.toString().padStart(2, '0')}</div>
              <div className="text-xs text-gray-500">Minutes</div>
            </div>
          </div>
        </motion.div>

        {/* Enhanced feature highlights with animations */}
        <motion.div
          className="mt-16 grid grid-cols-1 md:grid-cols-3 gap-8 text-center"
          initial={{ opacity: 0, y: 40 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.7 }}
        >
          {[
            {
              icon: "🚀",
              title: "AI-Powered Analysis",
              description: "Advanced Agentic AI framework that understands code structure, dependencies, and learning patterns"
            },
            {
              icon: "📚",
              title: "Beginner-Friendly",
              description: "Transform complex codebases into digestible, step-by-step learning experiences"
            },
            {
              icon: "⚡",
              title: "Instant Generation",
              description: "Generate comprehensive tutorials in minutes, not hours. Connect any GitHub repo instantly"
            }
          ].map((feature, index) => (
            <motion.div
              key={index}
              className="bg-white backdrop-blur-sm rounded-2xl p-8 border border-gray-200 hover:shadow-lg transition-all duration-300 cursor-pointer group"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 1.9 + index * 0.1 }}
              whileHover={{ y: -5 }}
            >
              <motion.div
                className="text-4xl mb-4"
                animate={{
                  rotate: [0, 10, -10, 0],
                  scale: [1, 1.1, 1]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: index * 0.5
                }}
              >
                {feature.icon}
              </motion.div>
              <h3 className="text-gray-900 font-bold text-xl mb-3 group-hover:text-gray-700 transition-colors">
                {feature.title}
              </h3>
              <p className="text-gray-600 text-sm leading-relaxed">
                {feature.description}
              </p>
            </motion.div>
          ))}
        </motion.div>

        {/* Final social proof and testimonial preview - minimalistic */}
        <motion.div
          className="mt-16 bg-gray-50 backdrop-blur-sm rounded-2xl p-8 border border-gray-200 max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 2.2 }}
        >
          <div className="flex flex-col md:flex-row items-center gap-6">
            <div className="flex-1 text-left">
              <div className="flex items-center gap-2 mb-3">
                {[...Array(5)].map((_, i) => (
                  <Star key={i} className="w-5 h-5 text-gray-600 fill-current" />
                ))}
                <span className="text-gray-600 text-sm ml-2">Loved by developers worldwide</span>
              </div>
              <blockquote className="text-gray-900 text-lg italic mb-3">
                "Finally, a tool that makes sense of complex codebases. This will save me hours every week!"
              </blockquote>
              <cite className="text-gray-500 text-sm">- Sarah Chen, Senior Developer at TechCorp</cite>
            </div>
            <div className="text-center">
              <div className="text-3xl font-bold text-gray-900">{userCount.toLocaleString()}+</div>
              <div className="text-gray-500 text-sm">Developers Waiting</div>
              <div className="text-gray-600 text-xs mt-1">Join them today!</div>
            </div>
          </div>
        </motion.div>
      </div>

      <JoinWaitingListModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
      />
    </div>
  );
};

export default HeroSection;
