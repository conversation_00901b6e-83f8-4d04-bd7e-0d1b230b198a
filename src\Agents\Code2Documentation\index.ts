// // src/Agents/Code2Documentation/index.ts
// import { createQaFlow } from './flow'
// import { SharedStore } from './types'

// // Example main function
// async function main(): Promise<void> {
//   const shared: SharedStore = {
//     question: undefined, // Will be populated by GetQuestionNode from user input
//     answer: undefined,
//     includePatterns: new Set<string>(),
//     excludePatterns: new Set<string>(),
//     maxFileSize: 0
//   }

//   // Create the flow and run it
//   const qaFlow = createQaFlow()
//   await qaFlow.run(shared)
//   console.log(`Question: ${shared.question}`)
//   console.log(`Answer: ${shared.answer}`)
// }

// // Run the main function
// main().catch(console.error)
