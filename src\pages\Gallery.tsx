
import React, { useState } from "react";
import { <PERSON> } from "react-router-dom";

import { BookOpen, Plus, Search } from "lucide-react";
import DeleteTutorialDialog from "@/components/DeleteTutorialDialog";
import { useAuth } from "@/hooks/useAuth";
import TutorialCard from "@/components/TutorialCard";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tutorial, useTutorials } from "@/hooks/useTutorials";
import { LoadingTutorialSkeleton } from "@/components/TutorialSkeleton";

const Gallery = () => {
  const { isSignedIn } = useAuth();
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedLanguage, setSelectedLanguage] = useState("");
  const [selectedDifficulty, setSelectedDifficulty] = useState("");

  const {
    tutorials,
    loading: recentLoading,
    error: recentError,
  } = useTutorials();

  // State for delete dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [tutorialToDelete, setTutorialToDelete] = useState<{
    id: string;
    title: string;
  } | null>(null);

  // Function to handle tutorial deletion - only for authenticated users
  const handleDeleteClick = (e: React.MouseEvent, tutorial: Tutorial) => {
    if (!isSignedIn) return; // Prevent deletion if not signed in

    e.preventDefault(); // Prevent navigation to tutorial details
    e.stopPropagation(); // Prevent event bubbling
    setTutorialToDelete({ id: tutorial.id, title: tutorial.title });
    setDeleteDialogOpen(true);
  };

  // Function to handle successful deletion
  const handleTutorialDeleted = () => {
    // Force a refresh of the tutorial lists
    window.location.reload();
    // Note: In a production app, you would update the state instead of reloading the page
  };

  return (
    <>
      {/* Delete Tutorial Dialog - only show if user is signed in */}
      {isSignedIn && tutorialToDelete && (
        <DeleteTutorialDialog
          isOpen={deleteDialogOpen}
          onClose={() => {
            setDeleteDialogOpen(false);
            setTutorialToDelete(null);
          }}
          tutorialId={tutorialToDelete.id}
          tutorialTitle={tutorialToDelete.title}
          onDeleted={handleTutorialDeleted}
        />
      )}

      {/* Page Title */}
      <div id="page-title" className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-800 mb-3">
          My Gallery
        </h1>
        <p className="text-gray-600 max-w-3xl">
          Browse through your collection of AI-generated tutorials from GitHub
          repositories. Find comprehensive guides for popular codebases and
          frameworks.
        </p>
      </div>

      {/* Search and Filter Section */}
      <div
        id="search-filter-section"
        className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-8"
      >
        <div className="flex flex-col md:flex-row gap-4">
          {/* Search Bar */}
          <div className="w-full md:w-1/2">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
              <Input
                type="text"
                className="pl-10 pr-4"
                placeholder="Search tutorials by name, language, or framework..."
              />
            </div>
          </div>

          {/* Filters */}
          <div className="w-full md:w-1/2 flex flex-wrap gap-3">
            <select className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500">
              <option value="">All Languages</option>
              <option value="javascript">JavaScript</option>
              <option value="typescript">TypeScript</option>
              <option value="python">Python</option>
              <option value="java">Java</option>
              <option value="go">Go</option>
              <option value="rust">Rust</option>
            </select>

            <select className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500">
              <option value="">All Difficulties</option>
              <option value="beginner">Beginner</option>
              <option value="intermediate">Intermediate</option>
              <option value="advanced">Advanced</option>
            </select>

            <select className="border border-gray-300 rounded-md px-3 py-2 bg-white focus:ring-primary-500 focus:border-primary-500">
              <option value="newest">Newest First</option>
              <option value="oldest">Oldest First</option>
              <option value="popular">Most Popular</option>
            </select>
          </div>
        </div>
      </div>

      {/* Recently Generated */}
      {RenderAll()}
    </>
  );

  function RenderEmpty(): React.ReactNode {
    return <div className="text-center py-16">
      <BookOpen className="mx-auto h-16 w-16 text-gray-400 mb-4" />
      <h3 className="text-xl font-medium text-gray-900 mb-2">
        {searchTerm || selectedLanguage || selectedDifficulty
          ? "No matching tutorials found"
          : "No tutorials yet"}
      </h3>
      <p className="text-gray-600 mb-6">
        {searchTerm || selectedLanguage || selectedDifficulty
          ? "Try adjusting your search or filters"
          : "Be the first to create amazing tutorials from your repositories"}
      </p>
      <Link to="/auth">
        <Button className="bg-primary-600 hover:bg-primary-700">
          Create First Tutorial
        </Button>
      </Link>
    </div>;
  }

  function RenderAll() {
    return (
      <div id="recently-generated" className="mb-12">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-semibold text-gray-800">
            Generated Tutorials
          </h2>
          <Button
            variant="link"
            className="text-primary-600 hover:text-primary-700"
            asChild
          >
            <Link to="/">
              <span className="mr-1">Generate New</span>
              <Plus className="h-4 w-4" />
            </Link>
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          {recentLoading ? (
            <>
              <LoadingTutorialSkeleton />
              <LoadingTutorialSkeleton />
              <LoadingTutorialSkeleton />
              <LoadingTutorialSkeleton />
            </>
          ) : recentError ? (
            <div className="col-span-4 text-center text-red-500 py-12">
              Error loading recent tutorials: {recentError}
            </div>
          ) : tutorials.length === 0 ? (
            <p> No tutorials yet</p>
          ) : (
            tutorials.map((tutorial) => (
              <TutorialCard
                key={tutorial.id}
                 tutorial={{
                ...tutorial,
                // Determine the primary type for the card
                // type: tutorial.types.includes("featured") 
                //   ? "featured" 
                //   : tutorial.types.includes("popular") 
                //   ? "popular" 
                //   : "recent",
                // Pass all types for multiple badges
                // allTypes: tutorial.types
              }}
                showDeleteButton={isSignedIn}
                onDeleteClick={handleDeleteClick}
              />
            ))
          )}
        </div>
      </div>
    );
  }
};

export default Gallery;
