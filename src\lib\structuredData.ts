// Structured Data (JSON-LD) generators for SEO

export interface Organization {
  "@type": "Organization";
  name: string;
  url: string;
  logo?: string;
  description?: string;
  foundingDate?: string;
  sameAs?: string[];
}

export interface WebSite {
  "@type": "WebSite";
  name: string;
  url: string;
  description?: string;
  potentialAction?: {
    "@type": "SearchAction";
    target: string;
    "query-input": string;
  };
}

export interface SoftwareApplication {
  "@type": "SoftwareApplication";
  name: string;
  description: string;
  url: string;
  applicationCategory: string;
  operatingSystem: string;
  offers?: {
    "@type": "Offer";
    price: string;
    priceCurrency: string;
    availability: string;
  };
  aggregateRating?: {
    "@type": "AggregateRating";
    ratingValue: number;
    reviewCount: number;
  };
}

export interface FAQPage {
  "@type": "FAQPage";
  mainEntity: Array<{
    "@type": "Question";
    name: string;
    acceptedAnswer: {
      "@type": "Answer";
      text: string;
    };
  }>;
}

// Generate organization structured data
export const generateOrganizationLD = (): Organization => ({
  "@type": "Organization",
  name: "CodeTutorPro",
  url: "https://codetutorpro.com",
  logo: "https://codetutorpro.com/logo.png",
  description: "AI-powered platform that transforms GitHub repositories into comprehensive, beginner-friendly tutorials",
  foundingDate: "2025",
  sameAs: [
    "https://twitter.com/codetutorpro",
    "https://github.com/codetutorpro",
    "https://linkedin.com/company/codetutorpro"
  ]
});

// Generate website structured data
export const generateWebSiteLD = (): WebSite => ({
  "@type": "WebSite",
  name: "CodeTutorPro",
  url: "https://codetutorpro.com",
  description: "Transform GitHub repositories into AI-powered, step-by-step tutorials for developers",
  potentialAction: {
    "@type": "SearchAction",
    target: "https://codetutorpro.com/search?q={search_term_string}",
    "query-input": "required name=search_term_string"
  }
});

// Generate software application structured data
export const generateSoftwareApplicationLD = (): SoftwareApplication => ({
  "@type": "SoftwareApplication",
  name: "CodeTutorPro",
  description: "AI-powered platform that transforms any GitHub repository into comprehensive, beginner-friendly tutorials using advanced agentic AI framework",
  url: "https://codetutorpro.com",
  applicationCategory: "DeveloperApplication",
  operatingSystem: "Web Browser",
  offers: {
    "@type": "Offer",
    price: "0",
    priceCurrency: "USD",
    availability: "https://schema.org/InStock"
  },
  aggregateRating: {
    "@type": "AggregateRating",
    ratingValue: 4.8,
    reviewCount: 150
  }
});

// Generate FAQ structured data
export const generateFAQLD = (): FAQPage => ({
  "@type": "FAQPage",
  mainEntity: [
    {
      "@type": "Question",
      name: "What is CodeTutorPro?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "CodeTutorPro is an AI-powered platform that transforms any GitHub repository into comprehensive, step-by-step tutorials. It uses advanced agentic AI to analyze codebases and create beginner-friendly learning experiences."
      }
    },
    {
      "@type": "Question",
      name: "How does CodeTutorPro work?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "Simply connect any GitHub repository to CodeTutorPro, and our AI will analyze the code structure, dependencies, and patterns to generate comprehensive tutorials that break down complex concepts into digestible learning modules."
      }
    },
    {
      "@type": "Question",
      name: "Is CodeTutorPro free?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "Yes! We're offering free early access to CodeTutorPro. Join our waiting list to be among the first to experience the platform before our official launch."
      }
    },
    {
      "@type": "Question",
      name: "What programming languages does CodeTutorPro support?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "CodeTutorPro supports all major programming languages including JavaScript, Python, Java, C++, Go, Rust, and more. Our AI can analyze and create tutorials for any codebase hosted on GitHub."
      }
    },
    {
      "@type": "Question",
      name: "When will CodeTutorPro launch?",
      acceptedAnswer: {
        "@type": "Answer",
        text: "CodeTutorPro is scheduled to launch in Q2 2025. Join our waiting list to get early access and be notified when we go live."
      }
    }
  ]
});

// Generate complete structured data for homepage
export const generateHomepageStructuredData = () => {
  return {
    "@context": "https://schema.org",
    "@graph": [
      generateOrganizationLD(),
      generateWebSiteLD(),
      generateSoftwareApplicationLD(),
      generateFAQLD()
    ]
  };
};
