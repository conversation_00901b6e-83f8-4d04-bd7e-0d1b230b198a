import { createClient } from '@supabase/supabase-js';
import { HERO_HEADLINE_EXPERIMENT, ALL_EXPERIMENTS } from '../src/config/abTestExperiments';

// Supabase configuration
const SUPABASE_URL = "https://axdtrqmggulirxskvwjg.supabase.co";
const SUPABASE_SERVICE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImF4ZHRycW1nZ3VsaXJ4c2t2d2pnIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NjgxNDYzMSwiZXhwIjoyMDYyMzkwNjMxfQ.K5cs1WF6A3m_-kBJw1x9fycMG80FTQMrL5lDDA8beUM";

// Create Supabase client with service role key for admin operations
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY);

async function seedExperiments() {
  console.log('🌱 Seeding A/B test experiments...');

  try {
    // Insert all experiments
    for (const experiment of ALL_EXPERIMENTS) {
      console.log(`📊 Inserting experiment: ${experiment.name}`);
      
      const { data, error } = await supabase
        .from('ab_experiments')
        .upsert({
          id: experiment.id,
          name: experiment.name,
          description: experiment.description,
          status: experiment.status,
          start_date: experiment.startDate?.toISOString(),
          end_date: experiment.endDate?.toISOString(),
          variants: experiment.variants,
          target_audience: experiment.targetAudience,
          conversion_goals: experiment.conversionGoals,
          metadata: experiment.metadata
        }, {
          onConflict: 'id'
        });

      if (error) {
        console.error(`❌ Error inserting experiment ${experiment.id}:`, error);
      } else {
        console.log(`✅ Successfully inserted experiment: ${experiment.id}`);
      }
    }

    console.log('🎉 All experiments seeded successfully!');
    
    // Verify the data
    const { data: experiments, error: fetchError } = await supabase
      .from('ab_experiments')
      .select('*');

    if (fetchError) {
      console.error('❌ Error fetching experiments:', fetchError);
    } else {
      console.log(`📈 Total experiments in database: ${experiments?.length || 0}`);
      experiments?.forEach(exp => {
        console.log(`  - ${exp.name} (${exp.status})`);
      });
    }

  } catch (error) {
    console.error('💥 Fatal error seeding experiments:', error);
    process.exit(1);
  }
}

// Run the seeding script
if (require.main === module) {
  seedExperiments()
    .then(() => {
      console.log('✨ Seeding completed!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Seeding failed:', error);
      process.exit(1);
    });
}

export { seedExperiments };
