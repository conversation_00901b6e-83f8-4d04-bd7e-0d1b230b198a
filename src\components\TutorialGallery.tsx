
import React from "react";
import { <PERSON> } from "react-router-dom";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { BookOpen } from "lucide-react";
import { Tutorial, useTutorials } from "@/hooks/useTutorials";
import { Skeleton } from "@/components/ui/skeleton";

type TutorialGalleryProps = {
  tutorials: Tutorial[];
};

// Loading skeleton component
const TutorialGallerySkeleton = () => {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[1, 2, 3].map((i) => (
        <Card key={i} className="h-full overflow-hidden">
          <CardHeader className="pb-3">
            <div className="space-y-1">
              <Skeleton className="h-6 w-3/4" />
              <Skeleton className="h-4 w-full" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex flex-wrap gap-2 mb-3">
              <Skeleton className="h-5 w-16" />
              <Skeleton className="h-5 w-20" />
            </div>
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-24" />
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
};

const TutorialGallery = ({ tutorials }: TutorialGalleryProps) => {
  return (
    <div className="container py-8 px-4 animate-fade-in">
      <div className="flex items-center justify-between mb-8">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Recent Tutorials</h1>
          <p className="text-muted-foreground">
            Browse through recently generated tutorials
          </p>
        </div>
      </div>

      {tutorials.length === 0 ? (
        <div className="text-center py-12">
          <BookOpen className="mx-auto h-12 w-12 text-muted-foreground" />
          <h3 className="mt-4 text-lg font-medium">No tutorials yet</h3>
          <p className="mt-2 text-muted-foreground">
            Generate your first tutorial to see it here
          </p>
          <Link
            to="/"
            className="mt-4 inline-flex items-center justify-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow transition-colors hover:bg-primary/90"
          >
            Generate Tutorial
          </Link>
        </div>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {tutorials.map((tutorial) => (
            <Link key={tutorial.id} to={`/tutorial/${tutorial.id}`} className="group">
              <Card className="h-full overflow-hidden transition-all hover:shadow-md">
                <CardHeader className="pb-3">
                  <div className="space-y-1">
                    <CardTitle className="group-hover:text-tutorial-primary transition-colors">
                      {tutorial.title}
                    </CardTitle>
                    <CardDescription className="line-clamp-2">
                      {tutorial.description}
                    </CardDescription>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2 mb-3">
                    {tutorial.tags.map((tag) => (
                      <span key={tag} className="tutorial-tag">
                        {tag}
                      </span>
                    ))}
                  </div>
                  <div className="flex items-center justify-between text-xs text-muted-foreground">
                    <p>{new Date(tutorial.createdAt).toLocaleDateString()}</p>
                    <p>{tutorial.chaptersCount} chapters</p>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>
      )}
    </div>
  );
};

export default TutorialGallery;
