
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';
import { useMonthlyTutorialUsage } from '@/hooks/useMonthlyTutorialUsage';
import { Skeleton } from '@/components/ui/skeleton';

export const MonthlyUsageDisplay = () => {
  const {
    monthlyTutorialsCreated,
    maxTutorialsPerMonth,
    currentBillingCycleEnd,
    loading,
    error
  } = useMonthlyTutorialUsage();

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <Skeleton className="h-5 w-32" />
          <Skeleton className="h-4 w-48" />
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <Skeleton className="h-2 w-full" />
            <Skeleton className="h-4 w-24" />
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-sm text-red-600">Error loading usage data</p>
        </CardContent>
      </Card>
    );
  }

  const isUnlimited = maxTutorialsPerMonth === -1;
  const remainingTutorials = isUnlimited ? 0 : Math.max(0, maxTutorialsPerMonth - monthlyTutorialsCreated);
  const usagePercentage = isUnlimited ? 0 : (monthlyTutorialsCreated / maxTutorialsPerMonth) * 100;

  const formatDate = (dateString: string | null) => {
    if (!dateString) return 'Not set';
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-lg">Monthly Usage</CardTitle>
        <CardDescription>
          Your tutorial creation usage this billing cycle
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {!isUnlimited && (
            <div>
              <div className="flex justify-between text-sm mb-2">
                <span>Used: {monthlyTutorialsCreated}</span>
                <span>Limit: {maxTutorialsPerMonth}</span>
              </div>
              <Progress value={usagePercentage} className="h-2" />
              <p className="text-sm text-muted-foreground mt-1">
                {remainingTutorials} tutorials remaining
              </p>
            </div>
          )}
          
          {isUnlimited && (
            <div className="text-center py-4">
              <p className="text-lg font-medium text-green-600">Unlimited Tutorials</p>
              <p className="text-sm text-muted-foreground">
                Created {monthlyTutorialsCreated} this month
              </p>
            </div>
          )}

          {currentBillingCycleEnd && (
            <div className="text-xs text-muted-foreground">
              Billing cycle ends: {formatDate(currentBillingCycleEnd)}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};
