
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { ChartContainer, ChartTooltip, ChartTooltipContent } from "@/components/ui/chart";
import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Cell, ResponsiveContainer } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";
import type { UsageStats } from "@/utils/usageAnalytics";

interface UsageChartsPanelProps {
  stats?: UsageStats;
  recentUsage?: any[];
  isLoading: boolean;
}

const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

const UsageChartsPanel = ({ stats, recentUsage, isLoading }: UsageChartsPanelProps) => {
  if (isLoading) {
    return (
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {Array.from({ length: 2 }).map((_, i) => (
          <Card key={i}>
            <CardHeader>
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-48" />
            </CardHeader>
            <CardContent>
              <Skeleton className="h-64 w-full" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!stats) return null;

  // Prepare data for cost by model chart
  const costByModelData = Object.entries(stats.costByModel).map(([model, cost]) => ({
    model: model.split('/').pop() || model, // Get just the model name
    cost: Number(cost),
    calls: stats.callsByModel[model] || 0,
  }));

  // Prepare data for calls by model pie chart
  const callsByModelData = Object.entries(stats.callsByModel).map(([model, calls]) => ({
    model: model.split('/').pop() || model,
    calls: Number(calls),
    cost: stats.costByModel[model] || 0,
  }));

  const chartConfig = {
    cost: {
      label: "Cost ($)",
    },
    calls: {
      label: "API Calls",
    },
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <Card>
        <CardHeader>
          <CardTitle>Cost by Model</CardTitle>
          <CardDescription>
            Total spending breakdown by AI model
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-64">
            <BarChart data={costByModelData}>
              <XAxis 
                dataKey="model" 
                tick={{ fontSize: 12 }}
                angle={-45}
                textAnchor="end"
                height={60}
              />
              <YAxis tick={{ fontSize: 12 }} />
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value: number) => [
                  `$${value.toFixed(4)}`,
                  "Cost"
                ]}
              />
              <Bar dataKey="cost" fill="#0088FE" />
            </BarChart>
          </ChartContainer>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Usage Distribution</CardTitle>
          <CardDescription>
            API calls distribution by model
          </CardDescription>
        </CardHeader>
        <CardContent>
          <ChartContainer config={chartConfig} className="h-64">
            <PieChart>
              <Pie
                data={callsByModelData}
                dataKey="calls"
                nameKey="model"
                cx="50%"
                cy="50%"
                outerRadius={80}
                label={({ model, calls, percent }) => 
                  `${model}: ${calls} (${(percent * 100).toFixed(1)}%)`
                }
              >
                {callsByModelData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                ))}
              </Pie>
              <ChartTooltip 
                content={<ChartTooltipContent />}
                formatter={(value: number, name: string) => [
                  value,
                  name === "calls" ? "API Calls" : name
                ]}
              />
            </PieChart>
          </ChartContainer>
        </CardContent>
      </Card>
    </div>
  );
};

export default UsageChartsPanel;
