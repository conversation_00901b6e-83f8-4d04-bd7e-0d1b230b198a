import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useABTest, useSimpleABTest, useFeatureFlag } from '@/hooks/useABTest';
import type { HeroHeadlineConfig } from '@/types/abTesting';

/**
 * Demo component showcasing different A/B testing patterns
 * This component demonstrates how to use the A/B testing infrastructure
 */
export const ABTestDemo: React.FC = () => {
  // Example 1: Full A/B test with custom configuration
  const defaultHeroConfig: HeroHeadlineConfig = {
    title: 'Default Title',
    subtitle: 'This is the default subtitle for testing',
    ctaText: 'Default CTA',
    emphasizedWords: ['testing']
  };

  const { 
    config: heroConfig, 
    variant: heroVariant, 
    isLoading: heroLoading,
    trackEvent: trackHeroEvent,
    trackConversion: trackHeroConversion 
  } = useABTest<HeroHeadlineConfig>('hero-headline-v1', defaultHeroConfig);

  // Example 2: Simple A/B test with predefined variants
  const buttonVariants = {
    control: { color: 'blue', text: 'Sign Up', size: 'medium' },
    variant1: { color: 'green', text: 'Get Started', size: 'large' },
    variant2: { color: 'red', text: 'Join Now', size: 'small' }
  };

  const { 
    config: buttonConfig, 
    variant: buttonVariant,
    trackEvent: trackButtonEvent 
  } = useSimpleABTest('button-test-demo', buttonVariants, 'control');

  // Example 3: Feature flag
  const { 
    config: featureConfig, 
    variant: featureVariant 
  } = useFeatureFlag('demo-feature-flag', false);

  const handleHeroClick = () => {
    trackHeroEvent('hero_cta_click', { 
      variant: heroVariant?.id,
      timestamp: new Date().toISOString() 
    });
  };

  const handleHeroConversion = () => {
    trackHeroConversion('demo_conversion');
  };

  const handleButtonClick = () => {
    trackButtonEvent('button_click', { 
      variant: buttonVariant?.id,
      color: buttonConfig.color 
    });
  };

  if (heroLoading) {
    return (
      <Card className="w-full max-w-4xl mx-auto">
        <CardContent className="p-6">
          <div className="text-center">Loading A/B test configurations...</div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-4xl mx-auto space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>A/B Testing Demo</CardTitle>
          <CardDescription>
            This component demonstrates the A/B testing infrastructure in action
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Hero Headline A/B Test */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">Hero Headline Test</h3>
              {heroVariant && (
                <Badge variant="outline">
                  Variant: {heroVariant.name}
                </Badge>
              )}
            </div>
            
            <div className="p-4 border rounded-lg bg-gray-50">
              <h1 className="text-2xl font-bold mb-2">{heroConfig.title}</h1>
              <p className="text-gray-600 mb-4">{heroConfig.subtitle}</p>
              <div className="flex gap-2">
                <Button onClick={handleHeroClick}>
                  {heroConfig.ctaText}
                </Button>
                <Button variant="outline" onClick={handleHeroConversion}>
                  Track Conversion
                </Button>
              </div>
            </div>
          </div>

          {/* Button A/B Test */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">Button Variant Test</h3>
              {buttonVariant && (
                <Badge variant="outline">
                  Variant: {buttonVariant.name}
                </Badge>
              )}
            </div>
            
            <div className="p-4 border rounded-lg bg-gray-50">
              <p className="mb-4">Testing different button styles and text:</p>
              <Button 
                onClick={handleButtonClick}
                style={{ 
                  backgroundColor: buttonConfig.color,
                  fontSize: buttonConfig.size === 'large' ? '18px' : 
                           buttonConfig.size === 'small' ? '12px' : '14px'
                }}
                className="text-white"
              >
                {buttonConfig.text}
              </Button>
              <div className="mt-2 text-sm text-gray-600">
                Color: {buttonConfig.color} | Size: {buttonConfig.size}
              </div>
            </div>
          </div>

          {/* Feature Flag */}
          <div className="space-y-4">
            <div className="flex items-center gap-2">
              <h3 className="text-lg font-semibold">Feature Flag Test</h3>
              {featureVariant && (
                <Badge variant="outline">
                  Variant: {featureVariant.name}
                </Badge>
              )}
            </div>
            
            <div className="p-4 border rounded-lg bg-gray-50">
              {featureConfig.enabled ? (
                <div className="text-green-600">
                  ✅ New feature is enabled for this user
                  <div className="mt-2 p-2 bg-green-100 rounded">
                    This is the new feature content that only some users see.
                  </div>
                </div>
              ) : (
                <div className="text-gray-600">
                  ❌ New feature is disabled for this user
                  <div className="mt-2 p-2 bg-gray-100 rounded">
                    This is the default content shown to users without the feature.
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Debug Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">Debug Information</h3>
            <div className="p-4 border rounded-lg bg-gray-50 text-sm">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <strong>Hero Test:</strong><br />
                  Experiment: hero-headline-v1<br />
                  Variant: {heroVariant?.id || 'none'}<br />
                  Loading: {heroLoading ? 'Yes' : 'No'}
                </div>
                <div>
                  <strong>Button Test:</strong><br />
                  Experiment: button-test-demo<br />
                  Variant: {buttonVariant?.id || 'none'}<br />
                  Config: {JSON.stringify(buttonConfig)}
                </div>
                <div>
                  <strong>Feature Flag:</strong><br />
                  Experiment: demo-feature-flag<br />
                  Variant: {featureVariant?.id || 'none'}<br />
                  Enabled: {featureConfig.enabled ? 'Yes' : 'No'}
                </div>
              </div>
            </div>
          </div>

        </CardContent>
      </Card>
    </div>
  );
};
