
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import Replicate from "https://esm.sh/replicate@0.25.2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { tutorialId, projectName, description, language } = await req.json()

    console.log(`Generating cover for tutorial: ${tutorialId}`)

    // Initialize Replicate
    const replicate = new Replicate({
      auth: Deno.env.get('REPLICATE_API_TOKEN'),
    })

    // Create a descriptive prompt for the cover image using description
    const prompt = `Professional tutorial cover for "${projectName}", ${description}, modern tech design, clean layout, code visualization elements, programming icons, gradient background, high quality, 16:9 aspect ratio`

    console.log(`Generated prompt: ${prompt}`)

    // Generate image using FLUX model
    const output = await replicate.run(
      "black-forest-labs/flux-schnell",
      {
        input: {
          prompt: prompt,
          go_fast: true,
          megapixels: "1",
          num_outputs: 1,
          aspect_ratio: "16:9",
          output_format: "webp",
          output_quality: 90,
          num_inference_steps: 4
        }
      }
    )

    console.log(`Image generated successfully`)

    // Download the generated image
    const imageUrl = Array.isArray(output) ? output[0] : output
    const imageResponse = await fetch(imageUrl)
    const imageBlob = await imageResponse.blob()

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Upload image to Supabase Storage
    const coverPath = `${tutorialId}/cover.webp`
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('tutorials')
      .upload(coverPath, imageBlob, {
        contentType: 'image/webp',
        upsert: true
      })

    if (uploadError) {
      console.error('Error uploading cover image:', uploadError)
      throw uploadError
    }

    console.log(`Cover uploaded to: ${coverPath}`)

    // Get public URL for the cover image with cache-busting parameter
    const { data: urlData } = supabase.storage
      .from('tutorials')
      .getPublicUrl(coverPath)

    const timestamp = Date.now()
    const coverUrl = `${urlData.publicUrl}?v=${timestamp}`

    // Update tutorial metadata with cover URL
    const { error: updateError } = await supabase
      .from('tutorial_metadata')
      .update({ cover_url: coverUrl })
      .eq('tutorial_id', tutorialId)

    if (updateError) {
      console.error('Error updating tutorial metadata:', updateError)
      throw updateError
    }

    console.log(`Tutorial metadata updated with cover URL: ${coverUrl}`)

    return new Response(
      JSON.stringify({ 
        success: true, 
        coverUrl: coverUrl,
        message: 'Cover image generated and saved successfully'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in generate-tutorial-cover function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to generate cover image', 
        details: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
        status: 500 
      }
    )
  }
})
