# System Patterns: CodeTutorPro

## Architecture Overview
CodeTutorPro follows a modern SaaS architecture with clear separation of concerns:

```
Frontend (React/TypeScript) → Backend (Supabase) → AI Processing (PocketFlow) → External APIs (GitHub, OpenAI)
```

## Core Design Patterns

### 1. Agent-Based AI Processing
**Pattern**: PocketFlow Agent Architecture
- **Code2Documentation Agent**: Handles the complete tutorial generation workflow
- **Shared Store Pattern**: Maintains state across processing nodes
- **Node-Based Processing**: Breaks down complex tasks into manageable steps

```typescript
// Example structure from src/Agents/Code2Documentation/
├── docs/          # High-level design documentation
├── flow/          # Workflow definitions
├── nodes/         # Individual processing steps
├── prompts/       # AI prompt templates
├── types.ts       # Type definitions
└── utils/         # Utility functions
```

### 2. Subscription-Based Access Control
**Pattern**: Tiered Feature Access
- **Trial System**: Time-limited access with usage constraints
- **Subscription Tiers**: Feature and usage limits based on payment tier
- **Usage Tracking**: Monthly limits with real-time monitoring

### 3. Repository Processing Pipeline
**Pattern**: Multi-Stage Data Processing
1. **Input Validation**: Repository URL and access verification
2. **Repository Crawling**: File discovery and filtering
3. **Content Analysis**: Code structure and dependency analysis
4. **Tutorial Generation**: AI-powered content creation
5. **Output Formatting**: Multi-format export (Markdown, HTML, PDF)

### 4. Component-Based UI Architecture
**Pattern**: Modular React Components
- **Layout Components**: Consistent page structure (LandingLayout, DashboardLayout, TutorialLayout)
- **Feature Components**: Specialized functionality (GitHubRepoCrawler, TutorialViewer)
- **UI Components**: Reusable design system (shadcn/ui)

## Key Architectural Decisions

### Frontend Architecture
- **React Router**: Client-side routing with protected routes
- **Zustand**: Lightweight state management for user data
- **React Query**: Server state management and caching
- **shadcn/ui**: Consistent, accessible component library

### Backend Integration
- **Supabase**: 
  - PostgreSQL database for user and tutorial data
  - Authentication and authorization
  - Real-time subscriptions for status updates
  - File storage for generated tutorials

### AI Processing
- **PocketFlow Framework**:
  - Lightweight LLM workflow orchestration
  - Node-based processing for complex tasks
  - Shared store for state management
  - Built-in retry and error handling

### External Integrations
- **GitHub API**: Repository access and file retrieval
- **Clerk**: User authentication and subscription management
- **OpenAI/OpenRouter**: LLM services for content generation

## Data Flow Patterns

### Tutorial Creation Flow
```mermaid
graph TD
    A[User Input] --> B[Repository Validation]
    B --> C[File Crawling]
    C --> D[Content Analysis]
    D --> E[AI Processing]
    E --> F[Tutorial Generation]
    F --> G[Output Formatting]
    G --> H[Storage & Delivery]
```

### User Management Flow
```mermaid
graph TD
    A[User Registration] --> B[Trial Activation]
    B --> C[Usage Tracking]
    C --> D{Limits Reached?}
    D -->|No| E[Continue Service]
    D -->|Yes| F[Upgrade Prompt]
    F --> G[Subscription Management]
    G --> H[Feature Unlock]
```

## Security Patterns
- **Authentication**: Clerk-based user authentication with JWT tokens
- **Authorization**: Role-based access control (user, admin)
- **API Security**: Protected routes with user context validation
- **Data Privacy**: Secure handling of GitHub tokens and repository data

## Performance Patterns
- **Lazy Loading**: Component and route-based code splitting
- **Caching**: React Query for API response caching
- **Optimistic Updates**: Immediate UI feedback with background processing
- **Progress Tracking**: Real-time status updates during tutorial generation

## Error Handling Patterns
- **Graceful Degradation**: Fallback UI states for failed operations
- **User Feedback**: Toast notifications and error messages
- **Retry Logic**: Automatic retry for transient failures
- **Logging**: Comprehensive error tracking for debugging
