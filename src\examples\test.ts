
import { asDiagramFlow } from '@/pocketflow/utils/diagramGenerator';
import { Flow, Node, BatchFlow } from '../pocketflow';

import * as fs from 'fs';
import * as path from 'path';

// Test class to verify diagram generation
class DiagramTester {
  // Test simple flow
  async testSimpleFlow(): Promise<boolean> {
    console.log('Testing simple flow diagram generation...');
    
    // Create a simple linear flow
    class NodeA extends Node {
      async exec(): Promise<string> {
        return "Node A executed";
      }
    }
    
    class NodeB extends Node {
      async exec(): Promise<string> {
        return "Node B executed";
      }
    }
    
    class NodeC extends Node {
      async exec(): Promise<string> {
        return "Node C executed";
      }
    }
    
    const nodeA = new NodeA();
    const nodeB = new NodeB();
    const nodeC = new NodeC();
    
    nodeA.next(nodeB).next(nodeC);
    
    const flow = new Flow(nodeA);
    const outputPath = './output/test-simple-flow.pgn';
    
    try {
     const diagramFlow = asDiagramFlow(flow);
     diagramFlow.export_png(outputPath);
      const exists = fs.existsSync(outputPath);
      console.log(`Simple flow diagram ${exists ? 'generated successfully' : 'generation failed'}`);
      return exists;
    } catch (error) {
      console.error('Error generating simple flow diagram:', error);
      return false;
    }
  }
  
  // Test branching flow
  async testBranchingFlow(): Promise<boolean> {
    console.log('Testing branching flow diagram generation...');
    
    // Create a flow with branches
    class DecisionNode extends Node {
      async exec(): Promise<string> {
        return "Decision made";
      }
      
      async post(shared: any): Promise<string> {
        return shared.condition ? "yes" : "no";
      }
    }
    
    class YesNode extends Node {
      async exec(): Promise<string> {
        return "Yes path taken";
      }
    }
    
    class NoNode extends Node {
      async exec(): Promise<string> {
        return "No path taken";
      }
    }
    
    class EndNode extends Node {
      async exec(): Promise<string> {
        return "Process completed";
      }
    }
    
    const decisionNode = new DecisionNode();
    const yesNode = new YesNode();
    const noNode = new NoNode();
    const endNode = new EndNode();
    
    decisionNode.on("yes", yesNode);
    decisionNode.on("no", noNode);
    yesNode.next(endNode);
    noNode.next(endNode);
    
    const flow = new Flow(decisionNode);
    const outputPath = './output/test-branching-flow.pgn';
    
    try {
      const diagramFlow = asDiagramFlow(flow);
      const exists = fs.existsSync(outputPath);
      console.log(`Branching flow diagram ${exists ? 'generated successfully' : 'generation failed'}`);
      return exists;
    } catch (error) {
      console.error('Error generating branching flow diagram:', error);
      return false;
    }
  }
  
  // Test flow with loops
  async testLoopFlow(): Promise<boolean> {
    console.log('Testing loop flow diagram generation...');
    
    // Create a flow with a loop
    class StartNode extends Node {
      async exec(): Promise<string> {
        return "Process started";
      }
    }
    
    class ProcessNode extends Node {
      async exec(): Promise<string> {
        return "Processing data";
      }
      
      async post(shared: any): Promise<string> {
        return shared.counter < 3 ? "repeat" : "complete";
      }
    }
    
    class UpdateCounterNode extends Node {
      async exec(shared: any): Promise<string> {
        shared.counter = (shared.counter || 0) + 1;
        return `Counter updated to ${shared.counter}`;
      }
    }
    
    class CompleteNode extends Node {
      async exec(): Promise<string> {
        return "Process completed";
      }
    }
    
    const startNode = new StartNode();
    const processNode = new ProcessNode();
    const updateCounterNode = new UpdateCounterNode();
    const completeNode = new CompleteNode();
    
    startNode.next(processNode);
    processNode.on("repeat", updateCounterNode);
    processNode.on("complete", completeNode);
    updateCounterNode.next(processNode); // Create loop
    
    const flow = new Flow(startNode);
    const outputPath = './output/test-loop-flow.svg';
    
    try {
     const diagramFlow = asDiagramFlow(flow);
      const exists = fs.existsSync(outputPath);
      console.log(`Loop flow diagram ${exists ? 'generated successfully' : 'generation failed'}`);
      return exists;
    } catch (error) {
      console.error('Error generating loop flow diagram:', error);
      return false;
    }
  }
  
  // Test nested flow
  async testNestedFlow(): Promise<boolean> {
    console.log('Testing nested flow diagram generation...');
    
    // Create a sub-flow
    class SubProcessA extends Node {
      async exec(): Promise<string> {
        return "Sub-process A executed";
      }
    }
    
    class SubProcessB extends Node {
      async exec(): Promise<string> {
        return "Sub-process B executed";
      }
    }
    
    const subProcessA = new SubProcessA();
    const subProcessB = new SubProcessB();
    subProcessA.next(subProcessB);
    const subFlow = new Flow(subProcessA);
    
    // Create main flow that uses the sub-flow
    class MainStart extends Node {
      async exec(): Promise<string> {
        return "Main process started";
      }
    }
    
    class MainEnd extends Node {
      async exec(): Promise<string> {
        return "Main process completed";
      }
    }
    
    const mainStart = new MainStart();
    const mainEnd = new MainEnd();
    
    mainStart.next(subFlow);
    subFlow.next(mainEnd);
    
    const mainFlow = new Flow(mainStart);
    const outputPath = './output/test-nested-flow.svg';
    
    try {
    const diagramFlow = asDiagramFlow(mainFlow);
      const exists = fs.existsSync(outputPath);
      console.log(`Nested flow diagram ${exists ? 'generated successfully' : 'generation failed'}`);
      return exists;
    } catch (error) {
      console.error('Error generating nested flow diagram:', error);
      return false;
    }
  }
  
  // Test batch flow
  async testBatchFlow(): Promise<boolean> {
    console.log('Testing batch flow diagram generation...');
    
    // Create a simple flow for batch processing
    class ProcessItem extends Node {
      async exec(shared: any): Promise<string> {
        return `Processing item ${shared.id}`;
      }
    }
    
    const processItem = new ProcessItem();
    const itemFlow = new Flow(processItem);
    
    // Create batch flow
    class ItemBatchFlow extends BatchFlow {
      async prep(): Promise<Record<string, any>[]> {
        return [
          { id: 1 },
          { id: 2 },
          { id: 3 }
        ];
      }
    }
    
    const batchFlow = new ItemBatchFlow(itemFlow);
    const outputPath = './output/test-batch-flow.svg';
    
    try {
      const diagramFlow = asDiagramFlow(batchFlow);
      const exists = fs.existsSync(outputPath);
      console.log(`Batch flow diagram ${exists ? 'generated successfully' : 'generation failed'}`);
      return exists;
    } catch (error) {
      console.error('Error generating batch flow diagram:', error);
      return false;
    }
  }
  
  // Run all tests
  async runAllTests(): Promise<void> {
    console.log('Starting diagram generator tests...');
    
    // Create output directory if it doesn't exist
    const outputDir = './output';
    if (!fs.existsSync(outputDir)) {
      fs.mkdirSync(outputDir, { recursive: true });
    }
    
    // Run all tests
    const results = await Promise.all([
      this.testSimpleFlow(),
      this.testBranchingFlow(),
      this.testLoopFlow(),
      this.testNestedFlow(),
      this.testBatchFlow()
    ]);
    
    // Report results
    const totalTests = results.length;
    const passedTests = results.filter(result => result).length;
    
    console.log('\nTest Results:');
    console.log(`Passed: ${passedTests}/${totalTests}`);
    
    if (passedTests === totalTests) {
      console.log('✅ All tests passed!');
    } else {
      console.log('❌ Some tests failed.');
    }
  }
}

// Run the tests
const tester = new DiagramTester();
tester.runAllTests().catch(error => {
  console.error('Error running tests:', error);
});
