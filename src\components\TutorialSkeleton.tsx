
import React from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card";

export const FeaturedTutorialSkeleton = () => {
  return (
    <Card className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="h-[180px] bg-gray-200 relative"></div>
      <div className="p-5">
        <div className="flex items-center justify-between mb-3">
          <Skeleton className="h-6 w-3/4" />
          <Skeleton className="h-5 w-20" />
        </div>
        <Skeleton className="h-4 w-full mb-2" />
        <Skeleton className="h-4 w-4/5 mb-4" />
        <div className="flex items-center justify-between">
          <Skeleton className="h-4 w-24" />
          <Skeleton className="h-4 w-24" />
        </div>
        <div className="mt-4 flex justify-between items-center">
          <div className="flex -space-x-2">
            <Skeleton className="w-6 h-6 rounded-full" />
            <Skeleton className="w-6 h-6 rounded-full" />
            <Skeleton className="w-6 h-6 rounded-full" />
          </div>
          <Skeleton className="h-8 w-24" />
        </div>
      </div>
    </Card>
  );
};


export const LoadingTutorialSkeleton = () => {
  return (
    <Card className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="h-[140px] bg-gray-200 relative"></div>
      <div className="p-4">
        <Skeleton className="h-5 w-4/5 mb-2" />
        <Skeleton className="h-3 w-full mb-2" />
        <Skeleton className="h-3 w-5/6 mb-3" />
        <div className="flex items-center justify-between">
          <Skeleton className="h-3 w-20" />
          <Skeleton className="h-3 w-20" />
        </div>
      </div>
    </Card>
  );
};

export const RecentTutorialSkeleton = () => {
  return (
    <Card className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
      <div className="h-[140px] bg-gray-200 relative"></div>
      <div className="p-4">
        <Skeleton className="h-5 w-4/5 mb-2" />
        <Skeleton className="h-3 w-full mb-2" />
        <Skeleton className="h-3 w-5/6 mb-3" />
        <div className="flex items-center justify-between">
          <Skeleton className="h-3 w-20" />
          <Skeleton className="h-3 w-20" />
        </div>
      </div>
    </Card>
  );
};

export const PopularTutorialSkeleton = () => {
  return (
    <Card className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden flex">
      <div className="w-1/3 bg-gray-200"></div>
      <div className="w-2/3 p-4">
        <div className="flex items-center mb-1">
          <Skeleton className="h-5 w-2/3" />
          <Skeleton className="ml-2 h-4 w-16" />
        </div>
        <Skeleton className="h-3 w-5/6 mb-2" />
        <div className="flex items-center mb-2">
          <Skeleton className="h-3 w-20 mr-2" />
          <Skeleton className="h-3 w-20" />
        </div>
        <div className="flex">
          <Skeleton className="w-5 h-5 rounded-full mr-1" />
          <Skeleton className="w-5 h-5 rounded-full mr-1" />
          <Skeleton className="w-5 h-5 rounded-full" />
        </div>
      </div>
    </Card>
  );
};
