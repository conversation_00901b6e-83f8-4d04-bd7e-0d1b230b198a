import fs from 'fs';
import path from 'path';

export interface LocalCrawlOptions {
  includePatterns?: Set<string>;
  excludePatterns?: Set<string>;
  maxFileSize?: number;
  useRelativePaths?: boolean;  // defaults to true
}

export interface LocalCrawlResult {
  files: Record<string, string>;
}

export async function fetch_selected_local_files(
  directory: string,
  selectedFiles: string[],
  options: { useRelativePaths?: boolean } = {}
): Promise<LocalCrawlResult> {
  const { useRelativePaths = true } = options;
  const result: Record<string, string> = {};

  for (const filePath of selectedFiles) {
    try {
      const fullPath = path.isAbsolute(filePath) ? filePath : path.join(directory, filePath);

      // Check if file exists
      if (!fs.existsSync(fullPath)) {
        console.warn(`File not found: ${fullPath}`);
        continue;
      }

      // Check if it's actually a file
      const stat = fs.statSync(fullPath);
      if (!stat.isFile()) {
        console.warn(`Path is not a file: ${fullPath}`);
        continue;
      }

      // Read file content
      const content = fs.readFileSync(fullPath, 'utf8');
      const finalPath = useRelativePaths ? path.relative(directory, fullPath) : fullPath;

      result[finalPath] = content;
    } catch (error) {
      console.warn(`Failed to read file ${filePath}:`, error);
    }
  }

  return { files: result };
}

export async function crawl_local_files(
  _directory: string,
  _options: LocalCrawlOptions = {}
): Promise<LocalCrawlResult> {
  // This function is deprecated in favor of fetch_selected_local_files
  // but kept for backward compatibility
  console.warn('crawl_local_files is deprecated. Use fetch_selected_local_files instead.');

  const result: Record<string, string> = {};
  return { files: result };
}