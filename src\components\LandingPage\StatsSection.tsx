
export function StatsSection() {
  const stats = [
    { value: "50K+", label: "Tutorials Generated" },
    { value: "15K+", label: "Happy Developers" },
    { value: "500+", label: "Companies" },
    { value: "98%", label: "Satisfaction Rate" }
  ];

  return (
    <section className="py-16 bg-tutorial-primary text-white">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-8 text-center">
          {stats.map((stat, index) => (
            <div key={index}>
              <div className="text-4xl font-bold mb-2">{stat.value}</div>
              <div className="text-blue-200">{stat.label}</div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}
