import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { supabase } from '@/integrations/supabase/client';
import { abTestingService } from '@/services/abTesting';
import type { ABTestExperiment, ABTestResults } from '@/types/abTesting';

interface ABTestManagerProps {
  className?: string;
}

export const ABTestManager: React.FC<ABTestManagerProps> = ({ className }) => {
  const [experiments, setExperiments] = useState<ABTestExperiment[]>([]);
  const [results, setResults] = useState<Record<string, ABTestResults>>({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadExperiments();
  }, []);

  const loadExperiments = async () => {
    try {
      setLoading(true);
      
      // Fetch experiments
      const { data: experimentsData, error } = await supabase
        .from('ab_experiments')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) {
        console.error('Error loading experiments:', error);
        return;
      }

      const formattedExperiments: ABTestExperiment[] = experimentsData?.map(exp => ({
        id: exp.id,
        name: exp.name,
        description: exp.description,
        status: exp.status as 'draft' | 'active' | 'paused' | 'completed',
        startDate: exp.start_date ? new Date(exp.start_date) : undefined,
        endDate: exp.end_date ? new Date(exp.end_date) : undefined,
        variants: exp.variants || [],
        targetAudience: exp.target_audience || {},
        conversionGoals: exp.conversion_goals || [],
        metadata: exp.metadata || {}
      })) || [];

      setExperiments(formattedExperiments);

      // Load results for active experiments
      const activeExperiments = formattedExperiments.filter(exp => exp.status === 'active');
      const resultsData: Record<string, ABTestResults> = {};
      
      for (const experiment of activeExperiments) {
        const experimentResults = await abTestingService.getExperimentResults(experiment.id);
        if (experimentResults) {
          resultsData[experiment.id] = experimentResults;
        }
      }
      
      setResults(resultsData);
    } catch (error) {
      console.error('Error loading experiments:', error);
    } finally {
      setLoading(false);
    }
  };

  const updateExperimentStatus = async (experimentId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('ab_experiments')
        .update({ status: newStatus })
        .eq('id', experimentId);

      if (error) {
        console.error('Error updating experiment status:', error);
        return;
      }

      // Reload experiments
      await loadExperiments();
    } catch (error) {
      console.error('Error updating experiment status:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'bg-green-500';
      case 'paused': return 'bg-yellow-500';
      case 'completed': return 'bg-blue-500';
      default: return 'bg-gray-500';
    }
  };

  const formatDate = (date?: Date) => {
    return date ? date.toLocaleDateString() : 'Not set';
  };

  if (loading) {
    return (
      <div className={`p-6 ${className}`}>
        <div className="text-center">Loading experiments...</div>
      </div>
    );
  }

  return (
    <div className={`p-6 space-y-6 ${className}`}>
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">A/B Test Manager</h2>
        <Button onClick={loadExperiments}>Refresh</Button>
      </div>

      <Tabs defaultValue="experiments" className="w-full">
        <TabsList>
          <TabsTrigger value="experiments">Experiments</TabsTrigger>
          <TabsTrigger value="results">Results</TabsTrigger>
        </TabsList>

        <TabsContent value="experiments" className="space-y-4">
          {experiments.map((experiment) => (
            <Card key={experiment.id}>
              <CardHeader>
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="flex items-center gap-2">
                      {experiment.name}
                      <Badge className={getStatusColor(experiment.status)}>
                        {experiment.status}
                      </Badge>
                    </CardTitle>
                    <CardDescription>{experiment.description}</CardDescription>
                  </div>
                  <div className="flex gap-2">
                    {experiment.status === 'draft' && (
                      <Button
                        size="sm"
                        onClick={() => updateExperimentStatus(experiment.id, 'active')}
                      >
                        Start
                      </Button>
                    )}
                    {experiment.status === 'active' && (
                      <>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => updateExperimentStatus(experiment.id, 'paused')}
                        >
                          Pause
                        </Button>
                        <Button
                          size="sm"
                          variant="destructive"
                          onClick={() => updateExperimentStatus(experiment.id, 'completed')}
                        >
                          Complete
                        </Button>
                      </>
                    )}
                    {experiment.status === 'paused' && (
                      <Button
                        size="sm"
                        onClick={() => updateExperimentStatus(experiment.id, 'active')}
                      >
                        Resume
                      </Button>
                    )}
                  </div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <strong>Start Date:</strong><br />
                    {formatDate(experiment.startDate)}
                  </div>
                  <div>
                    <strong>End Date:</strong><br />
                    {formatDate(experiment.endDate)}
                  </div>
                  <div>
                    <strong>Variants:</strong><br />
                    {experiment.variants.length}
                  </div>
                  <div>
                    <strong>Goals:</strong><br />
                    {experiment.conversionGoals?.length || 0}
                  </div>
                </div>
                
                <div className="mt-4">
                  <strong>Variants:</strong>
                  <div className="flex flex-wrap gap-2 mt-2">
                    {experiment.variants.map((variant) => (
                      <Badge key={variant.id} variant="outline">
                        {variant.name} ({variant.weight}%)
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="results" className="space-y-4">
          {Object.entries(results).map(([experimentId, result]) => {
            const experiment = experiments.find(exp => exp.id === experimentId);
            if (!experiment) return null;

            return (
              <Card key={experimentId}>
                <CardHeader>
                  <CardTitle>{experiment.name} - Results</CardTitle>
                  <CardDescription>
                    Total Views: {result.totalViews} | Total Conversions: {result.totalConversions}
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {result.variants.map((variant) => {
                      const conversionRate = variant.conversionRate || 0;
                      return (
                        <div key={variant.variantId} className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="font-medium">{variant.name}</span>
                            <span className="text-sm text-gray-600">
                              {variant.conversions}/{variant.views} ({conversionRate.toFixed(2)}%)
                            </span>
                          </div>
                          <Progress value={conversionRate} className="h-2" />
                        </div>
                      );
                    })}
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </TabsContent>
      </Tabs>
    </div>
  );
};
