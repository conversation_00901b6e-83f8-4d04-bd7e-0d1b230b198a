import { supabase } from "@/integrations/supabase/client";

/**
 * Call LLM with caching and usage tracking
 * @param store SharedStore to maintain llmCache
 * @param prompt The prompt string to send
 * @param temperature Sampling temperature
 * @returns Generated text
 */


export async function callLlm_openrouter({
  prompt,
  temperature = 0.2,
  model = "google/gemini-2.5-flash-preview-05-20",
  use_cache = true,
  user_id = null,
  session_id = null,
  tutorial_id,
}: {
  prompt: string;
  temperature?: number;
  model?: string;
  use_cache?: boolean;
  user_id?: string | null;
  session_id?: string | null;
  tutorial_id: string;
}): Promise<string> {


  //console.warn("  callLlm_openrouter", prompt);


  const url = "https://openrouter.ai/api/v1/chat/completions";

  const openRouterDevKey =
    "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";

  const options = {
    method: "POST",
    headers: {
      Authorization: `Bearer ${openRouterDevKey}`,
      "Content-Type": "application/json",
    },
    body: JSON.stringify({
      model,
      messages: [
        {
          role: "user",
          content: prompt,
        },
      ],
      temperature,
      usage: {
        include: true,
      },
    }),
  };

  try {
    const response = await fetch(url, options);
    const data = await response.json();

    // console.log("Full response data:", data);
    // console.log("Usage data:", data.usage);
    // console.log("Cost data:", data.usage?.cost);

    const output = data.choices[0].message?.content || "";

    // Store usage data in Supabase
    if (data.usage) {
      try {
        const usageData = {
          model: model,
          prompt_tokens: data.usage.prompt_tokens || null,
          completion_tokens: data.usage.completion_tokens || null,
          total_tokens: data.usage.total_tokens || null,
          cost: data.usage.cost || null,
          prompt_text:
            prompt.length > 1000 ? prompt.substring(0, 1000) + "..." : prompt,
          response_text:
            output.length > 1000 ? output.substring(0, 1000) + "..." : output,
          user_id: user_id,
          session_id: session_id,
          temperature: temperature,
          use_cache: use_cache,
          tutorial_id: tutorial_id,
        };

        const { error } = await supabase
          .from("openrouter_usage")
          .insert(usageData);

        if (error) {
          console.error("Error storing usage data:", error);
        } else {
          console.log("Usage data stored successfully:", usageData);
        }
      } catch (storageError) {
        console.error("Error storing usage data:", storageError);
      }
    }

    return output;
  } catch (error) {
    console.error("OpenRouter API error:", error);
    return `Error: ${error}`;
  }
}
