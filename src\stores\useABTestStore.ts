import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { ABTestExperiment, ABTestVariant, ABTestAssignment } from '@/types/abTesting';
import { abTestingService } from '@/services/abTesting';

interface ABTestState {
  // State
  experiments: Record<string, ABTestExperiment>;
  assignments: Record<string, ABTestAssignment>;
  loadingExperiments: Set<string>;
  
  // Actions
  getExperiment: (experimentId: string) => Promise<ABTestExperiment | null>;
  getVariant: (experimentId: string) => Promise<ABTestVariant | null>;
  trackEvent: (experimentId: string, eventType: string, eventData?: Record<string, any>) => Promise<void>;
  trackConversion: (experimentId: string, conversionGoal?: string) => Promise<void>;
  clearCache: () => void;
  
  // Internal actions
  setExperiment: (experiment: ABTestExperiment) => void;
  setAssignment: (assignment: ABTestAssignment) => void;
  setLoading: (experimentId: string, loading: boolean) => void;
}

export const useABTestStore = create<ABTestState>()(
  persist(
    (set, get) => ({
      // Initial state
      experiments: {},
      assignments: {},
      loadingExperiments: new Set(),

      // Get experiment and cache it
      getExperiment: async (experimentId: string) => {
        const state = get();
        
        // Return cached experiment if available and not expired
        const cachedExperiment = state.experiments[experimentId];
        if (cachedExperiment) {
          // Check if experiment is still active and not expired
          const now = new Date();
          if (cachedExperiment.status === 'active' && 
              (!cachedExperiment.endDate || cachedExperiment.endDate > now)) {
            return cachedExperiment;
          }
        }

        // Prevent multiple simultaneous requests
        if (state.loadingExperiments.has(experimentId)) {
          return null;
        }

        state.setLoading(experimentId, true);

        try {
          const experiment = await abTestingService.getExperiment(experimentId);
          if (experiment) {
            state.setExperiment(experiment);
          }
          return experiment;
        } finally {
          state.setLoading(experimentId, false);
        }
      },

      // Get variant for user
      getVariant: async (experimentId: string) => {
        const state = get();
        
        // Check for cached assignment
        const cachedAssignment = state.assignments[experimentId];
        if (cachedAssignment) {
          const experiment = await state.getExperiment(experimentId);
          if (experiment) {
            const variant = experiment.variants.find(v => v.id === cachedAssignment.variantId);
            if (variant) return variant;
          }
        }

        // Get or create assignment
        const assignment = await abTestingService.getOrCreateAssignment(experimentId);
        if (!assignment) return null;

        // Cache assignment
        state.setAssignment(assignment);

        // Get experiment and return variant
        const experiment = await state.getExperiment(experimentId);
        if (!experiment) return null;

        const variant = experiment.variants.find(v => v.id === assignment.variantId);
        
        // Track view event
        if (variant) {
          await abTestingService.trackEvent(experimentId, variant.id, 'view');
        }

        return variant || null;
      },

      // Track custom event
      trackEvent: async (experimentId: string, eventType: string, eventData?: Record<string, any>) => {
        const state = get();
        const assignment = state.assignments[experimentId];
        
        if (assignment) {
          await abTestingService.trackEvent(
            experimentId,
            assignment.variantId,
            'custom',
            eventType,
            eventData
          );
        }
      },

      // Track conversion
      trackConversion: async (experimentId: string, conversionGoal?: string) => {
        const state = get();
        const assignment = state.assignments[experimentId];
        
        if (assignment && !assignment.isConverted) {
          await abTestingService.trackConversion(
            experimentId,
            assignment.variantId,
            conversionGoal
          );
          
          // Update local assignment
          state.setAssignment({
            ...assignment,
            isConverted: true,
            conversionEvents: [...(assignment.conversionEvents || []), conversionGoal || 'default']
          });
        }
      },

      // Clear all cached data
      clearCache: () => {
        set({
          experiments: {},
          assignments: {},
          loadingExperiments: new Set()
        });
      },

      // Internal actions
      setExperiment: (experiment: ABTestExperiment) => {
        set(state => ({
          experiments: {
            ...state.experiments,
            [experiment.id]: experiment
          }
        }));
      },

      setAssignment: (assignment: ABTestAssignment) => {
        set(state => ({
          assignments: {
            ...state.assignments,
            [assignment.experimentId]: assignment
          }
        }));
      },

      setLoading: (experimentId: string, loading: boolean) => {
        set(state => {
          const newLoadingSet = new Set(state.loadingExperiments);
          if (loading) {
            newLoadingSet.add(experimentId);
          } else {
            newLoadingSet.delete(experimentId);
          }
          return {
            loadingExperiments: newLoadingSet
          };
        });
      }
    }),
    {
      name: 'ab-test-storage',
      partialize: (state) => ({
        experiments: state.experiments,
        assignments: state.assignments
      }),
      // Clear cache after 24 hours
      version: 1,
    }
  )
);
