
import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.7.1'
import Replicate from "https://esm.sh/replicate@0.25.2"

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders })
  }

  try {
    const { tutorialId } = await req.json()

    console.log(`Regenerating cover for tutorial: ${tutorialId}`)

    // Initialize Supabase client
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    )

    // Get the current user from the session
    const authHeader = req.headers.get('authorization')
    if (!authHeader) {
      return new Response(
        JSON.stringify({ error: 'Authorization required' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
          status: 401 
        }
      )
    }

    // Get user from the auth header
    const { data: { user }, error: userError } = await supabase.auth.getUser(
      authHeader.replace('Bearer ', '')
    )

    if (userError || !user) {
      return new Response(
        JSON.stringify({ error: 'Invalid authentication' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
          status: 401 
        }
      )
    }

    // Get tutorial metadata and verify ownership - using 'id' column instead of 'tutorial_id'
    const { data: tutorial, error: tutorialError } = await supabase
      .from('tutorial_metadata')
      .select('*')
      .eq('id', tutorialId)
      .eq('user_id', user.id)
      .single()

    if (tutorialError || !tutorial) {
      console.error('Tutorial query error:', tutorialError)
      return new Response(
        JSON.stringify({ error: 'Tutorial not found or access denied' }),
        { 
          headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
          status: 404 
        }
      )
    }

    // Initialize Replicate
    const replicate = new Replicate({
      auth: Deno.env.get('REPLICATE_API_TOKEN'),
    })

    // Create a descriptive prompt for the cover image using tutorial description
    const prompt = `Professional tutorial cover for "${tutorial.project_name}", ${tutorial.description}, modern tech design, clean layout, code visualization elements, programming icons, gradient background, high quality, 16:9 aspect ratio`

    console.log(`Generated prompt: ${prompt}`)

    // Generate image using FLUX model
    const output = await replicate.run(
      "black-forest-labs/flux-schnell",
      {
        input: {
          prompt: prompt,
          go_fast: true,
          megapixels: "1",
          num_outputs: 1,
          aspect_ratio: "16:9",
          output_format: "webp",
          output_quality: 90,
          num_inference_steps: 4
        }
      }
    )

    console.log(`Image generated successfully`)

    // Download the generated image
    const imageUrl = Array.isArray(output) ? output[0] : output
    const imageResponse = await fetch(imageUrl)
    const imageBlob = await imageResponse.blob()

    // Upload image to Supabase Storage (replace existing) - using tutorial_id for the path
    const coverPath = `${tutorial.tutorial_id}/cover.webp`
    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('tutorials')
      .upload(coverPath, imageBlob, {
        contentType: 'image/webp',
        upsert: true
      })

    if (uploadError) {
      console.error('Error uploading cover image:', uploadError)
      throw uploadError
    }

    console.log(`Cover uploaded to: ${coverPath}`)

    // Get public URL for the cover image with cache-busting parameter
    const { data: urlData } = supabase.storage
      .from('tutorials')
      .getPublicUrl(coverPath)

    const timestamp = Date.now()
    const coverUrl = `${urlData.publicUrl}?v=${timestamp}`

    // Update tutorial metadata with new cover URL - using 'id' column
    const { error: updateError } = await supabase
      .from('tutorial_metadata')
      .update({ 
        cover_url: coverUrl,
        updated_at: new Date().toISOString()
      })
      .eq('id', tutorialId)

    if (updateError) {
      console.error('Error updating tutorial metadata:', updateError)
      throw updateError
    }

    console.log(`Tutorial metadata updated with new cover URL: ${coverUrl}`)

    return new Response(
      JSON.stringify({ 
        success: true, 
        coverUrl: coverUrl,
        message: 'Cover image regenerated successfully'
      }),
      { headers: { ...corsHeaders, 'Content-Type': 'application/json' } }
    )

  } catch (error) {
    console.error('Error in regenerate-tutorial-cover function:', error)
    return new Response(
      JSON.stringify({ 
        error: 'Failed to regenerate cover image', 
        details: error.message 
      }),
      { 
        headers: { ...corsHeaders, 'Content-Type': 'application/json' }, 
        status: 500 
      }
    )
  }
})
