
import React, { useEffect, useRef } from "react";

import { Prism as Synta<PERSON><PERSON><PERSON>lighter } from "react-syntax-highlighter";
import { vscDarkPlus } from "react-syntax-highlighter/dist/esm/styles/prism";
import "highlight.js/styles/github-dark.css";
import ReactMarkdown from "react-markdown";
import remarkGfm from "remark-gfm";
import rehypeRaw from "rehype-raw";
import rehypeSanitize from "rehype-sanitize";
import rehypeSlug from "rehype-slug";
import rehypeAutolinkHeadings from "rehype-autolink-headings";
import rehypeExternalLinks from "rehype-external-links";
interface MarkdownRendererProps {
  content: string;
  showMarkdownSource?: boolean;
}

const MermaidDiagram = ({ chart }: { chart: string }) => {
  const ref = useRef<HTMLDivElement>(null);
  const id = `mermaid-${Math.random().toString(36).substring(2, 11)}`;

  useEffect(() => {
    if (ref.current) {
      import("mermaid").then(mermaid => {
        mermaid.default.initialize({
          startOnLoad: false,
          theme: 'default',
          securityLevel: 'loose',
          flowchart: { htmlLabels: true }
        });

        // Clear the element before rendering
        ref.current!.innerHTML = "";

        // Render the diagram
        mermaid.default.render(id, chart)
          .then(({ svg }) => {
            if (ref.current) {
              ref.current.innerHTML = svg;
            }
          })
          .catch(error => {
            console.error("Mermaid rendering failed:", error);
            if (ref.current) {
              ref.current.innerHTML = `<div class="p-4 text-red-500 bg-red-50 rounded">Error rendering diagram</div>`;
            }
          });
      });
    }
  }, [chart, id]);

  return <div ref={ref} className="my-6" />;
};

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({ content, showMarkdownSource = false }) => {

  // Custom renderers for markdown components
  const components = {
    // Custom inlineCode component to handle single backtick code
    inlineCode({ node, ...props }: any) {
      console.log("inlineCode component called with:", props.children);
      return (
        <span
          className="font-bold italic text-tutorial-primary border-b border-dashed border-tutorial-primary/50 px-0.5"
          title={`Code reference: ${props.children}`}
          {...props}
        />
      );
    },
    // Enhanced code block rendering with syntax highlighting
    code({ node, inline, className, children, ...props }: any) {
      console.log("code component called with:", {
        inline,
        className,
        children: String(children).substring(0, 50),
        nodeType: node?.type,
        props: Object.keys(props)
      });

      // Check if this is inline code (single backticks)
      if (inline) {
        return (
          <span
            className="font-bold italic text-tutorial-primary border-b border-dashed border-tutorial-primary/50 px-0.5"
            title={`Code reference: ${String(children)}`}
            {...props}
          >
            {children}
          </span>
        );
      }

      // Handle mermaid diagrams
      const match = /language-(\w+)/.exec(className || '');
      const language = match && match[1];

      if (language === 'mermaid') {
        const mermaidDiagram = String(children).replace(/\n$/, '');
        return (
          <div className="my-6">
            <MermaidDiagram chart={mermaidDiagram} />
          </div>
        );
      }

      // Handle regular code blocks
      return (
        <div className="relative my-4 group overflow-hidden w-full">
          {language && (
            <div className="absolute top-0 right-0 px-2 py-1 text-xs font-mono text-gray-400 bg-gray-800 rounded-bl z-10">
              {language}
            </div>
          )}
          <div className="overflow-x-auto max-w-full">
            <SyntaxHighlighter
              style={vscDarkPlus}
              language={language || "text"}
              PreTag="div"
              className="rounded-md"
              showLineNumbers={true}
              wrapLines={true}
              wrapLongLines={true}
              customStyle={{ maxWidth: '100%' }}
              {...props}
            >
              {String(children).replace(/\n$/, '')}
            </SyntaxHighlighter>
          </div>
          <button
            className="absolute top-0 right-10 px-2 py-1 text-xs text-gray-400 bg-gray-800 rounded-bl opacity-0 group-hover:opacity-100 transition-opacity"
            onClick={() => {
              navigator.clipboard.writeText(String(children));
            }}
          >
            Copy
          </button>
        </div>
      );
    },

    // Enhanced image rendering
    img({ node, ...props }: any) {
      return (
        <span className="block my-6">
          <img
            {...props}
            className="rounded-lg shadow-md max-w-full h-auto mx-auto"
            loading="lazy"
          />
          {props.alt && (
            <span className="block text-center text-sm text-gray-500 mt-2">
              {props.alt}
            </span>
          )}
        </span>
      );
    },

    // Enhanced table rendering
    table({ node, ...props }: any) {
      return (
        <div className="my-6 overflow-x-auto rounded-md border border-gray-200 dark:border-gray-700 max-w-full w-full">
          <table className="w-full divide-y divide-gray-200 dark:divide-gray-700 table-auto" style={{ maxWidth: '100%' }} {...props} />
        </div>
      );
    },

    thead({ node, ...props }: any) {
      return (
        <thead className="bg-gray-50 dark:bg-gray-800" {...props} />
      );
    },

    th({ node, ...props }: any) {
      return (
        <th
          className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
          {...props}
        />
      );
    },

    td({ node, ...props }: any) {
      return (
        <td
          className="px-6 py-4 text-sm text-gray-500 dark:text-gray-400 border-t border-gray-200 dark:border-gray-700 break-words"
          style={{ maxWidth: '300px', overflowWrap: 'break-word', wordWrap: 'break-word' }}
          {...props}
        />
      );
    },

    // Enhanced blockquote rendering
    blockquote({ node, ...props }: any) {
      return (
        <blockquote
          className="pl-4 border-l-4 border-gray-200 dark:border-gray-700 italic text-gray-700 dark:text-gray-300 my-6"
          {...props}
        />
      );
    },

    // Enhanced link rendering
    a({ node, ...props }: any) {
      const isExternal = props.href?.startsWith('http');
      return (
        <a
          className="text-tutorial-primary hover:text-tutorial-primary-dark underline decoration-1 underline-offset-2 transition-colors"
          target={isExternal ? "_blank" : undefined}
          rel={isExternal ? "noopener noreferrer" : undefined}
          {...props}
        />
      );
    },

    // Enhanced heading rendering with anchor links
    h1({ node, ...props }: any) {
      return <h1 className="text-3xl font-bold mt-8 mb-4 scroll-mt-16" {...props} />;
    },

    h2({ node, ...props }: any) {
      return <h2 className="text-2xl font-bold mt-6 mb-4 scroll-mt-16" {...props} />;
    },

    h3({ node, ...props }: any) {
      return <h3 className="text-xl font-bold mt-5 mb-3 scroll-mt-16" {...props} />;
    },

    h4({ node, ...props }: any) {
      return <h4 className="text-lg font-bold mt-4 mb-2 scroll-mt-16" {...props} />;
    },

    // Enhanced list rendering
    ul({ node, ordered, ...props }: any) {
      return <ul className="list-disc pl-6 my-4 space-y-2" {...props} />;
    },

    ol({ node, ordered, ...props }: any) {
      return <ol className="list-decimal pl-6 my-4 space-y-2" {...props} />;
    },

    li({ node, ordered, ...props }: any) {
      return <li className="pl-1" {...props} />;
    },

    // Enhanced checkbox rendering for task lists
    input({ node, ...props }: any) {
      if (props.type === 'checkbox') {
        return (
          <input
            {...props}
            className="mr-1.5 h-4 w-4 rounded border-gray-300 text-tutorial-primary focus:ring-tutorial-primary"
            disabled={false}
          />
        );
      }
      return <input {...props} />;
    },

    // Enhanced horizontal rule
    hr({ node, ...props }: any) {
      return <hr className="my-8 border-t border-gray-200 dark:border-gray-700" {...props} />;
    },
  };

  // Add debugging to see what's happening with the content
  //console.log("Rendering markdown content:", content.substring(0, 100));

  return (
    <div className="w-full max-w-full overflow-hidden">
      {showMarkdownSource ? (
        <div className="relative mt-10 font-mono text-sm bg-gray-50 dark:bg-gray-900 rounded-md border border-gray-200 dark:border-gray-700 overflow-hidden w-full">
          <div className="sticky top-0 bg-gray-100 dark:bg-gray-800 px-4 py-2 border-b border-gray-200 dark:border-gray-700 text-sm font-medium">
            Markdown Source
          </div>
          <div className="overflow-x-auto max-w-full">
            <SyntaxHighlighter
              language="markdown"
              style={vscDarkPlus}
              showLineNumbers={true}
              className="p-4 w-full"
              wrapLongLines={true}
              customStyle={{ maxWidth: '100%', overflowX: 'auto' }}
            >
              {content}
            </SyntaxHighlighter>
          </div>
        </div>
      ) : (
        <div className="prose prose-slate max-w-none dark:prose-invert prose-headings:font-bold prose-h1:text-3xl prose-h2:text-2xl prose-h3:text-xl prose-img:rounded-md prose-a:text-tutorial-primary prose-pre:bg-transparent prose-pre:p-0 prose-pre:rounded-md overflow-hidden break-words w-full">
         <>

          <ReactMarkdown
            remarkPlugins={[remarkGfm]}
            rehypePlugins={[
              rehypeRaw,
              rehypeSanitize,
              rehypeSlug,
              [rehypeAutolinkHeadings, { behavior: 'wrap' }],
              [rehypeExternalLinks, { target: '_blank', rel: ['nofollow', 'noopener', 'noreferrer'] }]
            ]}
            components={{
              ...components,
              // Handle inline code with the code component
              code: ({ node, inline, className, children, ...props }: any) => {
               // console.log("Direct code component:", { inline, className });

                // Handle inline code (single backticks)
                if (inline) {
                  return (
                    <span
                      className="font-bold italic text-tutorial-primary border-b border-dashed border-tutorial-primary/50 px-0.5"
                      title={`Code reference: ${String(children)}`}
                      {...props}
                    >
                      {children}
                    </span>
                  );
                }

                // For code blocks, we'll let the pre component handle it
                return (
                  <code className={className} {...props}>
                    {children}
                  </code>
                );
              },

              // Handle code blocks with the pre component
              pre: ({ node, children, ...props }: any) => {
                // Check if this is a code block
                if (children && children.props && children.props.className) {
                  const match = /language-(\w+)/.exec(children.props.className);
                  const language = match && match[1];
                  const code = children.props.children;

                  // Handle mermaid diagrams
                  if (language === 'mermaid') {
                    return (
                      <div className="my-6">
                        <MermaidDiagram chart={code} />
                      </div>
                    );
                  }

                  // Handle regular code blocks
                  return (
                    <div className="relative my-4 group overflow-hidden w-full">
                      {language && (
                        <div className="absolute top-0 right-0 px-2 py-1 text-xs font-mono text-gray-400 bg-gray-800 rounded-bl z-10">
                          {language}
                        </div>
                      )}
                      <div className="overflow-x-auto max-w-full">
                        <SyntaxHighlighter
                          style={vscDarkPlus}
                          language={language || "text"}
                          PreTag="div"
                          className="rounded-md"
                          showLineNumbers={true}
                          wrapLines={true}
                          wrapLongLines={true}
                          customStyle={{ maxWidth: '100%' }}
                        >
                          {code}
                        </SyntaxHighlighter>
                      </div>
                      <button
                        className="absolute top-0 right-10 px-2 py-1 text-xs text-gray-400 bg-gray-800 rounded-bl opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={() => {
                          navigator.clipboard.writeText(code);
                        }}
                      >
                        Copy
                      </button>
                    </div>
                  );
                }

                // If it's not a code block, just render a pre tag
                return <pre {...props}>{children}</pre>;
              }
            }}
          >
            {content}
          </ReactMarkdown>
            </>
        </div>
      )}
    </div>
  );
};

export default MarkdownRenderer;
